# generated by datamodel-codegen:
#   filename:  filtered-openapi.yaml
#   timestamp: 2025-05-04T04:12:39+00:00

from __future__ import annotations

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Literal, Optional, Union
from uuid import UUID

from pydantic import AnyUrl, BaseModel, Field, RootModel, StrictBytes


class PersonalAccessToken(BaseModel):
    id: Optional[UUID] = Field(None, description='Unique identifier for the GitCommit')
    name: Optional[str] = Field(
        None,
        description='Required. The name of the token. Can be a simple description.',
    )
    description: Optional[str] = Field(
        None,
        description="Optional. A more detailed description of the token's intended use.",
    )
    createdAt: Optional[datetime] = Field(
        None, description='[Output Only]The date and time the token was created.'
    )
    token: Optional[str] = Field(
        None,
        description='[Output Only]. The personal access token. Only returned during creation.',
    )


class GitCommitSummary(BaseModel):
    commit_hash: Optional[str] = Field(None, description='The hash of the commit')
    commit_name: Optional[str] = Field(None, description='The name of the commit')
    branch_name: Optional[str] = Field(
        None, description='The branch where the commit was made'
    )
    author: Optional[str] = Field(None, description='The author of the commit')
    timestamp: Optional[datetime] = Field(
        None, description='The timestamp when the commit was made'
    )
    status_summary: Optional[Dict[str, str]] = Field(
        None, description='A map of operating system to status pairs'
    )


class User(BaseModel):
    id: Optional[str] = Field(None, description='The unique id for this user.')
    email: Optional[str] = Field(None, description='The email address for this user.')
    name: Optional[str] = Field(None, description='The name for this user.')
    isApproved: Optional[bool] = Field(
        None, description='Indicates if the user is approved.'
    )
    isAdmin: Optional[bool] = Field(
        None, description='Indicates if the user has admin privileges.'
    )


class PublisherUser(BaseModel):
    id: Optional[str] = Field(None, description='The unique id for this user.')
    email: Optional[str] = Field(None, description='The email address for this user.')
    name: Optional[str] = Field(None, description='The name for this user.')


class ErrorResponse(BaseModel):
    error: str
    message: str


class StorageFile(BaseModel):
    id: Optional[UUID] = Field(
        None, description='Unique identifier for the storage file'
    )
    file_path: Optional[str] = Field(None, description='Path to the file in storage')
    public_url: Optional[str] = Field(None, description='Public URL')


class PublisherMember(BaseModel):
    id: Optional[str] = Field(
        None, description='The unique identifier for the publisher member.'
    )
    user: Optional[PublisherUser] = Field(
        None, description='The user associated with this publisher member.'
    )
    role: Optional[str] = Field(
        None, description='The role of the user in the publisher.'
    )


class ComfyNode(BaseModel):
    comfy_node_name: Optional[str] = Field(
        None, description='Unique identifier for the node'
    )
    category: Optional[str] = Field(
        None,
        description='UI category where the node is listed, used for grouping nodes.',
    )
    description: Optional[str] = Field(
        None, description="Brief description of the node's functionality or purpose."
    )
    input_types: Optional[str] = Field(None, description='Defines input parameters')
    deprecated: Optional[bool] = Field(
        None,
        description='Indicates if the node is deprecated. Deprecated nodes are hidden in the UI.',
    )
    experimental: Optional[bool] = Field(
        None,
        description='Indicates if the node is experimental, subject to changes or removal.',
    )
    output_is_list: Optional[List[bool]] = Field(
        None, description='Boolean values indicating if each output is a list.'
    )
    return_names: Optional[str] = Field(
        None, description='Names of the outputs for clarity in workflows.'
    )
    return_types: Optional[str] = Field(
        None, description='Specifies the types of outputs produced by the node.'
    )
    function: Optional[str] = Field(
        None, description='Name of the entry-point function to execute the node.'
    )


class ComfyNodeCloudBuildInfo(BaseModel):
    project_id: Optional[str] = None
    project_number: Optional[str] = None
    location: Optional[str] = None
    build_id: Optional[str] = None


class Error(BaseModel):
    message: Optional[str] = Field(
        None, description='A clear and concise description of the error.'
    )
    details: Optional[List[str]] = Field(
        None,
        description='Optional detailed information about the error or hints for resolving it.',
    )


class NodeVersionUpdateRequest(BaseModel):
    changelog: Optional[str] = Field(
        None, description='The changelog describing the version changes.'
    )
    deprecated: Optional[bool] = Field(
        None, description='Whether the version is deprecated.'
    )


class NodeStatus(str, Enum):
    NodeStatusActive = 'NodeStatusActive'
    NodeStatusDeleted = 'NodeStatusDeleted'
    NodeStatusBanned = 'NodeStatusBanned'


class NodeVersionStatus(str, Enum):
    NodeVersionStatusActive = 'NodeVersionStatusActive'
    NodeVersionStatusDeleted = 'NodeVersionStatusDeleted'
    NodeVersionStatusBanned = 'NodeVersionStatusBanned'
    NodeVersionStatusPending = 'NodeVersionStatusPending'
    NodeVersionStatusFlagged = 'NodeVersionStatusFlagged'


class PublisherStatus(str, Enum):
    PublisherStatusActive = 'PublisherStatusActive'
    PublisherStatusBanned = 'PublisherStatusBanned'


class WorkflowRunStatus(str, Enum):
    WorkflowRunStatusStarted = 'WorkflowRunStatusStarted'
    WorkflowRunStatusFailed = 'WorkflowRunStatusFailed'
    WorkflowRunStatusCompleted = 'WorkflowRunStatusCompleted'


class MachineStats(BaseModel):
    machine_name: Optional[str] = Field(None, description='Name of the machine.')
    os_version: Optional[str] = Field(
        None, description='The operating system version. eg. Ubuntu Linux 20.04'
    )
    gpu_type: Optional[str] = Field(
        None, description='The GPU type. eg. NVIDIA Tesla K80'
    )
    cpu_capacity: Optional[str] = Field(None, description='Total CPU on the machine.')
    initial_cpu: Optional[str] = Field(
        None, description='Initial CPU available before the job starts.'
    )
    memory_capacity: Optional[str] = Field(
        None, description='Total memory on the machine.'
    )
    initial_ram: Optional[str] = Field(
        None, description='Initial RAM available before the job starts.'
    )
    vram_time_series: Optional[Dict[str, Any]] = Field(
        None, description='Time series of VRAM usage.'
    )
    disk_capacity: Optional[str] = Field(
        None, description='Total disk capacity on the machine.'
    )
    initial_disk: Optional[str] = Field(
        None, description='Initial disk available before the job starts.'
    )
    pip_freeze: Optional[str] = Field(None, description='The pip freeze output')


class Customer(BaseModel):
    id: str = Field(..., description='The firebase UID of the user')
    email: Optional[str] = Field(None, description='The email address for this user')
    name: Optional[str] = Field(None, description='The name for this user')
    createdAt: Optional[datetime] = Field(
        None, description='The date and time the user was created'
    )
    updatedAt: Optional[datetime] = Field(
        None, description='The date and time the user was last updated'
    )


class MagicPrompt(str, Enum):
    ON = 'ON'
    OFF = 'OFF'


class ColorPalette(BaseModel):
    name: str = Field(..., description='Name of the color palette', examples=['PASTEL'])


class StyleCode(RootModel[str]):
    root: str = Field(..., pattern='^[0-9A-Fa-f]{8}$')


class StyleType(str, Enum):
    GENERAL = 'GENERAL'


class IdeogramColorPalette1(BaseModel):
    name: str = Field(..., description='Name of the preset color palette')


class Member(BaseModel):
    color: Optional[str] = Field(
        None, description='Hexadecimal color code', pattern='^#[0-9A-Fa-f]{6}$'
    )
    weight: Optional[float] = Field(
        None, description='Optional weight for the color (0-1)', ge=0.0, le=1.0
    )


class IdeogramColorPalette2(BaseModel):
    members: List[Member] = Field(
        ..., description='Array of color definitions with optional weights'
    )


class IdeogramColorPalette(
    RootModel[Union[IdeogramColorPalette1, IdeogramColorPalette2]]
):
    root: Union[IdeogramColorPalette1, IdeogramColorPalette2] = Field(
        ...,
        description='A color palette specification that can either use a preset name or explicit color definitions with weights',
    )


class ImageRequest(BaseModel):
    prompt: str = Field(
        ..., description='Required. The prompt to use to generate the image.'
    )
    aspect_ratio: Optional[str] = Field(
        None,
        description="Optional. The aspect ratio (e.g., 'ASPECT_16_9', 'ASPECT_1_1'). Cannot be used with resolution. Defaults to 'ASPECT_1_1' if unspecified.",
    )
    model: str = Field(..., description="The model used (e.g., 'V_2', 'V_2A_TURBO')")
    magic_prompt_option: Optional[str] = Field(
        None, description="Optional. MagicPrompt usage ('AUTO', 'ON', 'OFF')."
    )
    seed: Optional[int] = Field(
        None,
        description='Optional. A number between 0 and 2147483647.',
        ge=0,
        le=2147483647,
    )
    style_type: Optional[str] = Field(
        None,
        description="Optional. Style type ('AUTO', 'GENERAL', 'REALISTIC', 'DESIGN', 'RENDER_3D', 'ANIME'). Only for models V_2 and above.",
    )
    negative_prompt: Optional[str] = Field(
        None,
        description='Optional. Description of what to exclude. Only for V_1, V_1_TURBO, V_2, V_2_TURBO.',
    )
    num_images: Optional[int] = Field(
        1,
        description='Optional. Number of images to generate (1-8). Defaults to 1.',
        ge=1,
        le=8,
    )
    resolution: Optional[str] = Field(
        None,
        description="Optional. Resolution (e.g., 'RESOLUTION_1024_1024'). Only for model V_2. Cannot be used with aspect_ratio.",
    )
    color_palette: Optional[Dict[str, Any]] = Field(
        None, description='Optional. Color palette object. Only for V_2, V_2_TURBO.'
    )


class IdeogramGenerateRequest(BaseModel):
    image_request: ImageRequest = Field(
        ..., description='The image generation request parameters.'
    )


class Datum(BaseModel):
    prompt: Optional[str] = Field(
        None, description='The prompt used to generate this image.'
    )
    resolution: Optional[str] = Field(
        None, description="The resolution of the generated image (e.g., '1024x1024')."
    )
    is_image_safe: Optional[bool] = Field(
        None, description='Indicates whether the image is considered safe.'
    )
    seed: Optional[int] = Field(
        None, description='The seed value used for this generation.'
    )
    url: Optional[str] = Field(None, description='URL to the generated image.')
    style_type: Optional[str] = Field(
        None,
        description="The style type used for generation (e.g., 'REALISTIC', 'ANIME').",
    )


class IdeogramGenerateResponse(BaseModel):
    created: Optional[datetime] = Field(
        None, description='Timestamp when the generation was created.'
    )
    data: Optional[List[Datum]] = Field(
        None, description='Array of generated image information.'
    )


class RenderingSpeed1(str, Enum):
    TURBO = 'TURBO'
    DEFAULT = 'DEFAULT'
    QUALITY = 'QUALITY'


class MagicPrompt1(str, Enum):
    AUTO = 'AUTO'
    ON = 'ON'
    OFF = 'OFF'


class StyleType1(str, Enum):
    AUTO = 'AUTO'
    GENERAL = 'GENERAL'
    REALISTIC = 'REALISTIC'
    DESIGN = 'DESIGN'


class IdeogramV3RemixRequest(BaseModel):
    image: Optional[StrictBytes] = None
    prompt: str
    image_weight: Optional[int] = Field(50, ge=1, le=100)
    seed: Optional[int] = Field(None, ge=0, le=2147483647)
    resolution: Optional[str] = None
    aspect_ratio: Optional[str] = None
    rendering_speed: Optional[RenderingSpeed1] = None
    magic_prompt: Optional[MagicPrompt1] = None
    negative_prompt: Optional[str] = None
    num_images: Optional[int] = Field(None, ge=1, le=8)
    color_palette: Optional[Dict[str, Any]] = None
    style_codes: Optional[List[str]] = None
    style_type: Optional[StyleType1] = None
    style_reference_images: Optional[List[StrictBytes]] = None


class Datum1(BaseModel):
    prompt: Optional[str] = None
    resolution: Optional[str] = None
    is_image_safe: Optional[bool] = None
    seed: Optional[int] = None
    url: Optional[str] = None
    style_type: Optional[str] = None


class IdeogramV3IdeogramResponse(BaseModel):
    created: Optional[datetime] = None
    data: Optional[List[Datum1]] = None


class IdeogramV3ReframeRequest(BaseModel):
    image: Optional[StrictBytes] = None
    resolution: str
    num_images: Optional[int] = Field(None, ge=1, le=8)
    seed: Optional[int] = Field(None, ge=0, le=2147483647)
    rendering_speed: Optional[RenderingSpeed1] = None
    color_palette: Optional[Dict[str, Any]] = None
    style_codes: Optional[List[str]] = None
    style_reference_images: Optional[List[StrictBytes]] = None


class IdeogramV3ReplaceBackgroundRequest(BaseModel):
    image: Optional[StrictBytes] = None
    prompt: str
    magic_prompt: Optional[MagicPrompt1] = None
    num_images: Optional[int] = Field(None, ge=1, le=8)
    seed: Optional[int] = Field(None, ge=0, le=2147483647)
    rendering_speed: Optional[RenderingSpeed1] = None
    color_palette: Optional[Dict[str, Any]] = None
    style_codes: Optional[List[str]] = None
    style_reference_images: Optional[List[StrictBytes]] = None


class KlingTaskStatus(str, Enum):
    submitted = 'submitted'
    processing = 'processing'
    succeed = 'succeed'
    failed = 'failed'


class KlingVideoGenModelName(str, Enum):
    kling_v1 = 'kling-v1'
    kling_v1_5 = 'kling-v1-5'
    kling_v1_6 = 'kling-v1-6'
    kling_v2_master = 'kling-v2-master'


class KlingVideoGenMode(str, Enum):
    std = 'std'
    pro = 'pro'


class KlingVideoGenAspectRatio(str, Enum):
    field_16_9 = '16:9'
    field_9_16 = '9:16'
    field_1_1 = '1:1'


class KlingVideoGenDuration(str, Enum):
    field_5 = '5'
    field_10 = '10'


class KlingVideoGenCfgScale(RootModel[float]):
    root: float = Field(
        ...,
        description="Flexibility in video generation. The higher the value, the lower the model's degree of flexibility, and the stronger the relevance to the user's prompt.",
        ge=0.0,
        le=1.0,
    )


class KlingCameraControlType(str, Enum):
    simple = 'simple'
    down_back = 'down_back'
    forward_up = 'forward_up'
    right_turn_forward = 'right_turn_forward'
    left_turn_forward = 'left_turn_forward'


class KlingCameraConfig(BaseModel):
    horizontal: Optional[float] = Field(
        None,
        description="Controls camera's movement along horizontal axis (x-axis). Negative indicates left, positive indicates right.",
        ge=-10.0,
        le=10.0,
    )
    vertical: Optional[float] = Field(
        None,
        description="Controls camera's movement along vertical axis (y-axis). Negative indicates downward, positive indicates upward.",
        ge=-10.0,
        le=10.0,
    )
    pan: Optional[float] = Field(
        None,
        description="Controls camera's rotation in vertical plane (x-axis). Negative indicates downward rotation, positive indicates upward rotation.",
        ge=-10.0,
        le=10.0,
    )
    tilt: Optional[float] = Field(
        None,
        description="Controls camera's rotation in horizontal plane (y-axis). Negative indicates left rotation, positive indicates right rotation.",
        ge=-10.0,
        le=10.0,
    )
    roll: Optional[float] = Field(
        None,
        description="Controls camera's rolling amount (z-axis). Negative indicates counterclockwise, positive indicates clockwise.",
        ge=-10.0,
        le=10.0,
    )
    zoom: Optional[float] = Field(
        None,
        description="Controls change in camera's focal length. Negative indicates narrower field of view, positive indicates wider field of view.",
        ge=-10.0,
        le=10.0,
    )


class KlingVideoResult(BaseModel):
    id: Optional[str] = Field(None, description='Generated video ID')
    url: Optional[AnyUrl] = Field(None, description='URL for generated video')
    duration: Optional[str] = Field(None, description='Total video duration')


class KlingAudioUploadType(str, Enum):
    file = 'file'
    url = 'url'


class KlingLipSyncMode(str, Enum):
    text2video = 'text2video'
    audio2video = 'audio2video'


class KlingLipSyncVoiceLanguage(str, Enum):
    zh = 'zh'
    en = 'en'


class KlingDualCharacterEffectsScene(str, Enum):
    hug = 'hug'
    kiss = 'kiss'
    heart_gesture = 'heart_gesture'


class KlingSingleImageEffectsScene(str, Enum):
    bloombloom = 'bloombloom'
    dizzydizzy = 'dizzydizzy'
    fuzzyfuzzy = 'fuzzyfuzzy'
    squish = 'squish'
    expansion = 'expansion'


class KlingCharacterEffectModelName(str, Enum):
    kling_v1 = 'kling-v1'
    kling_v1_5 = 'kling-v1-5'
    kling_v1_6 = 'kling-v1-6'


class KlingSingleImageEffectModelName(str, Enum):
    kling_v1_6 = 'kling-v1-6'


class KlingSingleImageEffectDuration(str, Enum):
    field_5 = '5'


class KlingDualCharacterImages(RootModel[List[str]]):
    root: List[str] = Field(..., max_length=2, min_length=2)


class KlingImageGenAspectRatio(str, Enum):
    field_16_9 = '16:9'
    field_9_16 = '9:16'
    field_1_1 = '1:1'
    field_4_3 = '4:3'
    field_3_4 = '3:4'
    field_3_2 = '3:2'
    field_2_3 = '2:3'
    field_21_9 = '21:9'


class KlingImageGenImageReferenceType(str, Enum):
    subject = 'subject'
    face = 'face'


class KlingImageGenModelName(str, Enum):
    kling_v1 = 'kling-v1'
    kling_v1_5 = 'kling-v1-5'
    kling_v2 = 'kling-v2'


class KlingImageResult(BaseModel):
    index: Optional[int] = Field(None, description='Image Number (0-9)')
    url: Optional[AnyUrl] = Field(None, description='URL for generated image')


class KlingVirtualTryOnModelName(str, Enum):
    kolors_virtual_try_on_v1 = 'kolors-virtual-try-on-v1'
    kolors_virtual_try_on_v1_5 = 'kolors-virtual-try-on-v1-5'


class TaskInfo(BaseModel):
    external_task_id: Optional[str] = None


class TaskResult(BaseModel):
    videos: Optional[List[KlingVideoResult]] = None


class Data(BaseModel):
    task_id: Optional[str] = Field(None, description='Task ID')
    task_status: Optional[KlingTaskStatus] = None
    task_info: Optional[TaskInfo] = None
    created_at: Optional[int] = Field(None, description='Task creation time')
    updated_at: Optional[int] = Field(None, description='Task update time')
    task_result: Optional[TaskResult] = None


class KlingText2VideoResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    message: Optional[str] = Field(None, description='Error message')
    request_id: Optional[str] = Field(None, description='Request ID')
    data: Optional[Data] = None


class Trajectory(BaseModel):
    x: Optional[int] = Field(
        None,
        description='The horizontal coordinate of trajectory point. Based on bottom-left corner of image as origin (0,0).',
    )
    y: Optional[int] = Field(
        None,
        description='The vertical coordinate of trajectory point. Based on bottom-left corner of image as origin (0,0).',
    )


class DynamicMask(BaseModel):
    mask: Optional[AnyUrl] = Field(
        None,
        description='Dynamic Brush Application Area (Mask image created by users using the motion brush). The aspect ratio must match the input image.',
    )
    trajectories: Optional[List[Trajectory]] = None


class Data1(BaseModel):
    task_id: Optional[str] = Field(None, description='Task ID')
    task_status: Optional[KlingTaskStatus] = None
    task_info: Optional[TaskInfo] = None
    created_at: Optional[int] = Field(None, description='Task creation time')
    updated_at: Optional[int] = Field(None, description='Task update time')
    task_result: Optional[TaskResult] = None


class KlingImage2VideoResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    message: Optional[str] = Field(None, description='Error message')
    request_id: Optional[str] = Field(None, description='Request ID')
    data: Optional[Data1] = None


class KlingVideoExtendRequest(BaseModel):
    video_id: Optional[str] = Field(
        None,
        description='The ID of the video to be extended. Supports videos generated by text-to-video, image-to-video, and previous video extension operations. Cannot exceed 3 minutes total duration after extension.',
    )
    prompt: Optional[str] = Field(
        None,
        description='Positive text prompt for guiding the video extension',
        max_length=2500,
    )
    negative_prompt: Optional[str] = Field(
        None,
        description='Negative text prompt for elements to avoid in the extended video',
        max_length=2500,
    )
    cfg_scale: Optional[KlingVideoGenCfgScale] = Field(
        default_factory=lambda: KlingVideoGenCfgScale.model_validate(0.5)
    )
    callback_url: Optional[AnyUrl] = Field(
        None,
        description='The callback notification address. Server will notify when the task status changes.',
    )


class Data2(BaseModel):
    task_id: Optional[str] = Field(None, description='Task ID')
    task_status: Optional[KlingTaskStatus] = None
    task_info: Optional[TaskInfo] = None
    created_at: Optional[int] = Field(None, description='Task creation time')
    updated_at: Optional[int] = Field(None, description='Task update time')
    task_result: Optional[TaskResult] = None


class KlingVideoExtendResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    message: Optional[str] = Field(None, description='Error message')
    request_id: Optional[str] = Field(None, description='Request ID')
    data: Optional[Data2] = None


class KlingLipSyncInputObject(BaseModel):
    video_id: Optional[str] = Field(
        None,
        description='The ID of the video generated by Kling AI. Only supports 5-second and 10-second videos generated within the last 30 days.',
    )
    video_url: Optional[str] = Field(
        None,
        description='Get link for uploaded video. Video files support .mp4/.mov, file size does not exceed 100MB, video length between 2-10s.',
    )
    mode: KlingLipSyncMode
    text: Optional[str] = Field(
        None,
        description='Text Content for Lip-Sync Video Generation. Required when mode is text2video. Maximum length is 120 characters.',
    )
    voice_id: Optional[str] = Field(
        None,
        description='Voice ID. Required when mode is text2video. The system offers a variety of voice options to choose from.',
    )
    voice_language: Optional[KlingLipSyncVoiceLanguage] = 'en'
    voice_speed: Optional[float] = Field(
        1,
        description='Speech Rate. Valid range: 0.8~2.0, accurate to one decimal place.',
        ge=0.8,
        le=2.0,
    )
    audio_type: Optional[KlingAudioUploadType] = None
    audio_file: Optional[str] = Field(
        None,
        description='Local Path of Audio File. Supported formats: .mp3/.wav/.m4a/.aac, maximum file size of 5MB. Base64 code.',
    )
    audio_url: Optional[str] = Field(
        None,
        description='Audio File Download URL. Supported formats: .mp3/.wav/.m4a/.aac, maximum file size of 5MB.',
    )


class KlingLipSyncRequest(BaseModel):
    input: KlingLipSyncInputObject
    callback_url: Optional[AnyUrl] = Field(
        None,
        description='The callback notification address. Server will notify when the task status changes.',
    )


class Data3(BaseModel):
    task_id: Optional[str] = Field(None, description='Task ID')
    task_status: Optional[KlingTaskStatus] = None
    task_info: Optional[TaskInfo] = None
    created_at: Optional[int] = Field(None, description='Task creation time')
    updated_at: Optional[int] = Field(None, description='Task update time')
    task_result: Optional[TaskResult] = None


class KlingLipSyncResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    message: Optional[str] = Field(None, description='Error message')
    request_id: Optional[str] = Field(None, description='Request ID')
    data: Optional[Data3] = None


class KlingSingleImageEffectInput(BaseModel):
    model_name: KlingSingleImageEffectModelName
    image: str = Field(
        ...,
        description='Reference Image. URL or Base64 encoded string (without data:image prefix). File size cannot exceed 10MB, resolution not less than 300*300px, aspect ratio between 1:2.5 ~ 2.5:1.',
    )
    duration: KlingSingleImageEffectDuration


class KlingDualCharacterEffectInput(BaseModel):
    model_name: Optional[KlingCharacterEffectModelName] = 'kling-v1'
    mode: Optional[KlingVideoGenMode] = 'std'
    images: KlingDualCharacterImages
    duration: KlingVideoGenDuration


class Data4(BaseModel):
    task_id: Optional[str] = Field(None, description='Task ID')
    task_status: Optional[KlingTaskStatus] = None
    task_info: Optional[TaskInfo] = None
    created_at: Optional[int] = Field(None, description='Task creation time')
    updated_at: Optional[int] = Field(None, description='Task update time')
    task_result: Optional[TaskResult] = None


class KlingVideoEffectsResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    message: Optional[str] = Field(None, description='Error message')
    request_id: Optional[str] = Field(None, description='Request ID')
    data: Optional[Data4] = None


class KlingImageGenerationsRequest(BaseModel):
    model_name: Optional[KlingImageGenModelName] = 'kling-v1'
    prompt: str = Field(..., description='Positive text prompt', max_length=500)
    negative_prompt: Optional[str] = Field(
        None, description='Negative text prompt', max_length=200
    )
    image: Optional[str] = Field(
        None, description='Reference Image - Base64 encoded string or image URL'
    )
    image_reference: Optional[KlingImageGenImageReferenceType] = None
    image_fidelity: Optional[float] = Field(
        0.5, description='Reference intensity for user-uploaded images', ge=0.0, le=1.0
    )
    human_fidelity: Optional[float] = Field(
        0.45, description='Subject reference similarity', ge=0.0, le=1.0
    )
    n: Optional[int] = Field(1, description='Number of generated images', ge=1, le=9)
    aspect_ratio: Optional[KlingImageGenAspectRatio] = '16:9'
    callback_url: Optional[AnyUrl] = Field(
        None, description='The callback notification address'
    )


class TaskResult5(BaseModel):
    images: Optional[List[KlingImageResult]] = None


class Data5(BaseModel):
    task_id: Optional[str] = Field(None, description='Task ID')
    task_status: Optional[KlingTaskStatus] = None
    task_status_msg: Optional[str] = Field(None, description='Task status information')
    created_at: Optional[int] = Field(None, description='Task creation time')
    updated_at: Optional[int] = Field(None, description='Task update time')
    task_result: Optional[TaskResult5] = None


class KlingImageGenerationsResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    message: Optional[str] = Field(None, description='Error message')
    request_id: Optional[str] = Field(None, description='Request ID')
    data: Optional[Data5] = None


class KlingVirtualTryOnRequest(BaseModel):
    model_name: Optional[KlingVirtualTryOnModelName] = 'kolors-virtual-try-on-v1'
    human_image: str = Field(
        ..., description='Reference human image - Base64 encoded string or image URL'
    )
    cloth_image: Optional[str] = Field(
        None,
        description='Reference clothing image - Base64 encoded string or image URL',
    )
    callback_url: Optional[AnyUrl] = Field(
        None, description='The callback notification address'
    )


class Data6(BaseModel):
    task_id: Optional[str] = Field(None, description='Task ID')
    task_status: Optional[KlingTaskStatus] = None
    task_status_msg: Optional[str] = Field(None, description='Task status information')
    created_at: Optional[int] = Field(None, description='Task creation time')
    updated_at: Optional[int] = Field(None, description='Task update time')
    task_result: Optional[TaskResult5] = None


class KlingVirtualTryOnResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    message: Optional[str] = Field(None, description='Error message')
    request_id: Optional[str] = Field(None, description='Request ID')
    data: Optional[Data6] = None


class ResourcePackType(str, Enum):
    decreasing_total = 'decreasing_total'
    constant_period = 'constant_period'


class Status(str, Enum):
    toBeOnline = 'toBeOnline'
    online = 'online'
    expired = 'expired'
    runOut = 'runOut'


class ResourcePackSubscribeInfo(BaseModel):
    resource_pack_name: Optional[str] = Field(None, description='Resource package name')
    resource_pack_id: Optional[str] = Field(None, description='Resource package ID')
    resource_pack_type: Optional[ResourcePackType] = Field(
        None,
        description='Resource package type (decreasing_total=decreasing total, constant_period=constant periodicity)',
    )
    total_quantity: Optional[float] = Field(None, description='Total quantity')
    remaining_quantity: Optional[float] = Field(
        None, description='Remaining quantity (updated with a 12-hour delay)'
    )
    purchase_time: Optional[int] = Field(
        None, description='Purchase time, Unix timestamp in ms'
    )
    effective_time: Optional[int] = Field(
        None, description='Effective time, Unix timestamp in ms'
    )
    invalid_time: Optional[int] = Field(
        None, description='Expiration time, Unix timestamp in ms'
    )
    status: Optional[Status] = Field(None, description='Resource Package Status')


class Data7(BaseModel):
    code: Optional[int] = Field(None, description='Error code; 0 indicates success')
    msg: Optional[str] = Field(None, description='Error information')
    resource_pack_subscribe_infos: Optional[List[ResourcePackSubscribeInfo]] = Field(
        None, description='Resource package list'
    )


class KlingResourcePackageResponse(BaseModel):
    code: Optional[int] = Field(None, description='Error code; 0 indicates success')
    message: Optional[str] = Field(None, description='Error information')
    request_id: Optional[str] = Field(
        None,
        description='Request ID, generated by the system, used to track requests and troubleshoot problems',
    )
    data: Optional[Data7] = None


class Object(str, Enum):
    event = 'event'


class Type(str, Enum):
    payment_intent_succeeded = 'payment_intent.succeeded'


class StripeRequestInfo(BaseModel):
    id: Optional[str] = None
    idempotency_key: Optional[str] = None


class Object1(str, Enum):
    payment_intent = 'payment_intent'


class StripeAmountDetails(BaseModel):
    tip: Optional[Dict[str, Any]] = None


class Object2(str, Enum):
    charge = 'charge'


class StripeAddress(BaseModel):
    city: Optional[str] = None
    country: Optional[str] = None
    line1: Optional[str] = None
    line2: Optional[str] = None
    postal_code: Optional[str] = None
    state: Optional[str] = None


class StripeOutcome(BaseModel):
    advice_code: Optional[Any] = None
    network_advice_code: Optional[Any] = None
    network_decline_code: Optional[Any] = None
    network_status: Optional[str] = None
    reason: Optional[Any] = None
    risk_level: Optional[str] = None
    risk_score: Optional[int] = None
    seller_message: Optional[str] = None
    type: Optional[str] = None


class Checks(BaseModel):
    address_line1_check: Optional[Any] = None
    address_postal_code_check: Optional[Any] = None
    cvc_check: Optional[str] = None


class ExtendedAuthorization(BaseModel):
    status: Optional[str] = None


class IncrementalAuthorization(BaseModel):
    status: Optional[str] = None


class Multicapture(BaseModel):
    status: Optional[str] = None


class NetworkToken(BaseModel):
    used: Optional[bool] = None


class Overcapture(BaseModel):
    maximum_amount_capturable: Optional[int] = None
    status: Optional[str] = None


class StripeCardDetails(BaseModel):
    amount_authorized: Optional[int] = None
    authorization_code: Optional[Any] = None
    brand: Optional[str] = None
    checks: Optional[Checks] = None
    country: Optional[str] = None
    exp_month: Optional[int] = None
    exp_year: Optional[int] = None
    extended_authorization: Optional[ExtendedAuthorization] = None
    fingerprint: Optional[str] = None
    funding: Optional[str] = None
    incremental_authorization: Optional[IncrementalAuthorization] = None
    installments: Optional[Any] = None
    last4: Optional[str] = None
    mandate: Optional[Any] = None
    multicapture: Optional[Multicapture] = None
    network: Optional[str] = None
    network_token: Optional[NetworkToken] = None
    network_transaction_id: Optional[str] = None
    overcapture: Optional[Overcapture] = None
    regulated_status: Optional[str] = None
    three_d_secure: Optional[Any] = None
    wallet: Optional[Any] = None


class StripeRefundList(BaseModel):
    object: Optional[str] = None
    data: Optional[List[Dict[str, Any]]] = None
    has_more: Optional[bool] = None
    total_count: Optional[int] = None
    url: Optional[str] = None


class Card(BaseModel):
    installments: Optional[Any] = None
    mandate_options: Optional[Any] = None
    network: Optional[Any] = None
    request_three_d_secure: Optional[str] = None


class StripePaymentMethodOptions(BaseModel):
    card: Optional[Card] = None


class StripeShipping(BaseModel):
    address: Optional[StripeAddress] = None
    carrier: Optional[str] = None
    name: Optional[str] = None
    phone: Optional[str] = None
    tracking_number: Optional[str] = None


class Model(str, Enum):
    T2V_01_Director = 'T2V-01-Director'
    I2V_01_Director = 'I2V-01-Director'
    S2V_01 = 'S2V-01'
    I2V_01 = 'I2V-01'
    I2V_01_live = 'I2V-01-live'
    T2V_01 = 'T2V-01'


class SubjectReferenceItem(BaseModel):
    image: Optional[str] = Field(
        None, description='URL or base64 encoding of the subject reference image.'
    )
    mask: Optional[str] = Field(
        None,
        description='URL or base64 encoding of the mask for the subject reference image.',
    )


class MinimaxVideoGenerationRequest(BaseModel):
    model: Model = Field(
        ...,
        description='Required. ID of model. Options: T2V-01-Director, I2V-01-Director, S2V-01, I2V-01, I2V-01-live, T2V-01',
    )
    prompt: Optional[str] = Field(
        None,
        description='Description of the video. Should be less than 2000 characters. Supports camera movement instructions in [brackets].',
        max_length=2000,
    )
    prompt_optimizer: Optional[bool] = Field(
        True,
        description='If true (default), the model will automatically optimize the prompt. Set to false for more precise control.',
    )
    first_frame_image: Optional[str] = Field(
        None,
        description='URL or base64 encoding of the first frame image. Required when model is I2V-01, I2V-01-Director, or I2V-01-live.',
    )
    subject_reference: Optional[List[SubjectReferenceItem]] = Field(
        None,
        description='Only available when model is S2V-01. The model will generate a video based on the subject uploaded through this parameter.',
    )
    callback_url: Optional[str] = Field(
        None,
        description='Optional. URL to receive real-time status updates about the video generation task.',
    )


class MinimaxBaseResponse(BaseModel):
    status_code: int = Field(
        ...,
        description='Status code. 0 indicates success, other values indicate errors.',
    )
    status_msg: str = Field(
        ..., description='Specific error details or success message.'
    )


class MinimaxVideoGenerationResponse(BaseModel):
    task_id: str = Field(
        ..., description='The task ID for the asynchronous video generation task.'
    )
    base_resp: MinimaxBaseResponse


class File(BaseModel):
    file_id: Optional[int] = Field(None, description='Unique identifier for the file')
    bytes: Optional[int] = Field(None, description='File size in bytes')
    created_at: Optional[int] = Field(
        None, description='Unix timestamp when the file was created, in seconds'
    )
    filename: Optional[str] = Field(None, description='The name of the file')
    purpose: Optional[str] = Field(None, description='The purpose of using the file')
    download_url: Optional[str] = Field(
        None, description='The URL to download the video'
    )


class MinimaxFileRetrieveResponse(BaseModel):
    file: File
    base_resp: MinimaxBaseResponse


class Status1(str, Enum):
    Queueing = 'Queueing'
    Preparing = 'Preparing'
    Processing = 'Processing'
    Success = 'Success'
    Fail = 'Fail'


class MinimaxTaskResultResponse(BaseModel):
    task_id: str = Field(..., description='The task ID being queried.')
    status: Status1 = Field(
        ...,
        description="Task status: 'Queueing' (in queue), 'Preparing' (task is preparing), 'Processing' (generating), 'Success' (task completed successfully), or 'Fail' (task failed).",
    )
    file_id: Optional[str] = Field(
        None,
        description='After the task status changes to Success, this field returns the file ID corresponding to the generated video.',
    )
    base_resp: MinimaxBaseResponse


class OutputFormat(str, Enum):
    jpeg = 'jpeg'
    png = 'png'


class BFLFluxPro11GenerateRequest(BaseModel):
    prompt: str = Field(..., description='The main text prompt for image generation')
    image_prompt: Optional[str] = Field(None, description='Optional image prompt')
    width: int = Field(..., description='Width of the generated image')
    height: int = Field(..., description='Height of the generated image')
    prompt_upsampling: Optional[bool] = Field(
        None, description='Whether to use prompt upsampling'
    )
    seed: Optional[int] = Field(None, description='Random seed for reproducibility')
    safety_tolerance: Optional[int] = Field(None, description='Safety tolerance level')
    output_format: Optional[OutputFormat] = Field(
        None, description='Output image format'
    )
    webhook_url: Optional[str] = Field(
        None, description='Optional webhook URL for async processing'
    )
    webhook_secret: Optional[str] = Field(
        None, description='Optional webhook secret for async processing'
    )


class BFLFluxPro11GenerateResponse(BaseModel):
    id: str = Field(..., description='Job ID for tracking')
    polling_url: str = Field(..., description='URL to poll for results')


class BFLFluxProGenerateRequest(BaseModel):
    prompt: str = Field(..., description='The text prompt for image generation.')
    negative_prompt: Optional[str] = Field(
        None, description='The negative prompt for image generation.'
    )
    width: int = Field(
        ..., description='The width of the image to generate.', ge=64, le=2048
    )
    height: int = Field(
        ..., description='The height of the image to generate.', ge=64, le=2048
    )
    num_inference_steps: Optional[int] = Field(
        None, description='The number of inference steps.', ge=1, le=100
    )
    guidance_scale: Optional[float] = Field(
        None, description='The guidance scale for generation.', ge=1.0, le=20.0
    )
    seed: Optional[int] = Field(None, description='The seed value for reproducibility.')
    num_images: Optional[int] = Field(
        None, description='The number of images to generate.', ge=1, le=4
    )


class BFLFluxProGenerateResponse(BaseModel):
    id: str = Field(..., description='The unique identifier for the generation task.')
    polling_url: str = Field(..., description='URL to poll for the generation result.')


class Steps(RootModel[int]):
    root: int = Field(
        ...,
        description='Number of steps for the image generation process',
        examples=[50],
        ge=15,
        le=50,
        title='Steps',
    )


class Guidance(RootModel[float]):
    root: float = Field(
        ...,
        description='Guidance strength for the image generation process',
        ge=1.5,
        le=100.0,
        title='Guidance',
    )


class WebhookUrl(RootModel[AnyUrl]):
    root: AnyUrl = Field(
        ..., description='URL to receive webhook notifications', title='Webhook Url'
    )


class BFLAsyncResponse(BaseModel):
    id: str = Field(..., title='Id')
    polling_url: str = Field(..., title='Polling Url')


class BFLAsyncWebhookResponse(BaseModel):
    id: str = Field(..., title='Id')
    status: str = Field(..., title='Status')
    webhook_url: str = Field(..., title='Webhook Url')


class Top(RootModel[int]):
    root: int = Field(
        ...,
        description='Number of pixels to expand at the top of the image',
        ge=0,
        le=2048,
        title='Top',
    )


class Bottom(RootModel[int]):
    root: int = Field(
        ...,
        description='Number of pixels to expand at the bottom of the image',
        ge=0,
        le=2048,
        title='Bottom',
    )


class Left(RootModel[int]):
    root: int = Field(
        ...,
        description='Number of pixels to expand on the left side of the image',
        ge=0,
        le=2048,
        title='Left',
    )


class Right(RootModel[int]):
    root: int = Field(
        ...,
        description='Number of pixels to expand on the right side of the image',
        ge=0,
        le=2048,
        title='Right',
    )


class CannyLowThreshold(RootModel[int]):
    root: int = Field(
        ...,
        description='Low threshold for Canny edge detection',
        ge=0,
        le=500,
        title='Canny Low Threshold',
    )


class CannyHighThreshold(RootModel[int]):
    root: int = Field(
        ...,
        description='High threshold for Canny edge detection',
        ge=0,
        le=500,
        title='Canny High Threshold',
    )


class Steps2(RootModel[int]):
    root: int = Field(
        ...,
        description='Number of steps for the image generation process',
        ge=15,
        le=50,
        title='Steps',
    )


class Guidance2(RootModel[float]):
    root: float = Field(
        ...,
        description='Guidance strength for the image generation process',
        ge=1.0,
        le=100.0,
        title='Guidance',
    )


class BFLOutputFormat(str, Enum):
    jpeg = 'jpeg'
    png = 'png'


class BFLValidationError(BaseModel):
    loc: List[Union[str, int]] = Field(..., title='Location')
    msg: str = Field(..., title='Message')
    type: str = Field(..., title='Error Type')


class Datum2(BaseModel):
    image_id: Optional[str] = Field(
        None, description='Unique identifier for the generated image'
    )
    url: Optional[str] = Field(None, description='URL to access the generated image')


class RecraftImageGenerationResponse(BaseModel):
    created: int = Field(
        ..., description='Unix timestamp when the generation was created'
    )
    credits: int = Field(..., description='Number of credits used for the generation')
    data: List[Datum2] = Field(..., description='Array of generated image information')


class RecraftImageFeatures(BaseModel):
    nsfw_score: Optional[float] = None


class RecraftTextLayoutItem(BaseModel):
    bbox: List[List[float]]
    text: str


class RecraftImageColor(BaseModel):
    rgb: Optional[List[int]] = None
    std: Optional[List[float]] = None
    weight: Optional[float] = None


class RecraftImageStyle(str, Enum):
    digital_illustration = 'digital_illustration'
    icon = 'icon'
    realistic_image = 'realistic_image'
    vector_illustration = 'vector_illustration'


class RecraftImageSubStyle(str, Enum):
    field_2d_art_poster = '2d_art_poster'
    field_3d = '3d'
    field_80s = '80s'
    glow = 'glow'
    grain = 'grain'
    hand_drawn = 'hand_drawn'
    infantile_sketch = 'infantile_sketch'
    kawaii = 'kawaii'
    pixel_art = 'pixel_art'
    psychedelic = 'psychedelic'
    seamless = 'seamless'
    voxel = 'voxel'
    watercolor = 'watercolor'
    broken_line = 'broken_line'
    colored_outline = 'colored_outline'
    colored_shapes = 'colored_shapes'
    colored_shapes_gradient = 'colored_shapes_gradient'
    doodle_fill = 'doodle_fill'
    doodle_offset_fill = 'doodle_offset_fill'
    offset_fill = 'offset_fill'
    outline = 'outline'
    outline_gradient = 'outline_gradient'
    uneven_fill = 'uneven_fill'
    field_70s = '70s'
    cartoon = 'cartoon'
    doodle_line_art = 'doodle_line_art'
    engraving = 'engraving'
    flat_2 = 'flat_2'
    kawaii_1 = 'kawaii'
    line_art = 'line_art'
    linocut = 'linocut'
    seamless_1 = 'seamless'
    b_and_w = 'b_and_w'
    enterprise = 'enterprise'
    hard_flash = 'hard_flash'
    hdr = 'hdr'
    motion_blur = 'motion_blur'
    natural_light = 'natural_light'
    studio_portrait = 'studio_portrait'
    line_circuit = 'line_circuit'
    field_2d_art_poster_2 = '2d_art_poster_2'
    engraving_color = 'engraving_color'
    flat_air_art = 'flat_air_art'
    hand_drawn_outline = 'hand_drawn_outline'
    handmade_3d = 'handmade_3d'
    stickers_drawings = 'stickers_drawings'
    plastic = 'plastic'
    pictogram = 'pictogram'


class RecraftTransformModel(str, Enum):
    refm1 = 'refm1'
    recraft20b = 'recraft20b'
    recraftv2 = 'recraftv2'
    recraftv3 = 'recraftv3'
    flux1_1pro = 'flux1_1pro'
    flux1dev = 'flux1dev'
    imagen3 = 'imagen3'
    hidream_i1_dev = 'hidream_i1_dev'


class RecraftImageFormat(str, Enum):
    webp = 'webp'
    png = 'png'


class RecraftResponseFormat(str, Enum):
    url = 'url'
    b64_json = 'b64_json'


class RecraftImage(BaseModel):
    b64_json: Optional[str] = None
    features: Optional[RecraftImageFeatures] = None
    image_id: UUID
    revised_prompt: Optional[str] = None
    url: Optional[str] = None


class RecraftUserControls(BaseModel):
    artistic_level: Optional[int] = None
    background_color: Optional[RecraftImageColor] = None
    colors: Optional[List[RecraftImageColor]] = None
    no_text: Optional[bool] = None


class RecraftTextLayout(RootModel[List[RecraftTextLayoutItem]]):
    root: List[RecraftTextLayoutItem]


class RecraftProcessImageRequest(BaseModel):
    image: StrictBytes
    image_format: Optional[RecraftImageFormat] = None
    response_format: Optional[RecraftResponseFormat] = None


class RecraftProcessImageResponse(BaseModel):
    created: int
    credits: int
    image: RecraftImage


class RecraftImageToImageRequest(BaseModel):
    block_nsfw: Optional[bool] = None
    calculate_features: Optional[bool] = None
    controls: Optional[RecraftUserControls] = None
    image: StrictBytes
    image_format: Optional[RecraftImageFormat] = None
    model: Optional[RecraftTransformModel] = None
    n: Optional[int] = None
    negative_prompt: Optional[str] = None
    prompt: str
    random_seed: Optional[int] = None
    response_format: Optional[RecraftResponseFormat] = None
    strength: float
    style: Optional[RecraftImageStyle] = None
    style_id: Optional[UUID] = None
    substyle: Optional[RecraftImageSubStyle] = None
    text_layout: Optional[RecraftTextLayout] = None


class RecraftGenerateImageResponse(BaseModel):
    created: int
    credits: int
    data: List[RecraftImage]


class RecraftTransformImageWithMaskRequest(BaseModel):
    block_nsfw: Optional[bool] = None
    calculate_features: Optional[bool] = None
    image: StrictBytes
    image_format: Optional[RecraftImageFormat] = None
    mask: StrictBytes
    model: Optional[RecraftTransformModel] = None
    n: Optional[int] = None
    negative_prompt: Optional[str] = None
    prompt: str
    random_seed: Optional[int] = None
    response_format: Optional[RecraftResponseFormat] = None
    style: Optional[RecraftImageStyle] = None
    style_id: Optional[UUID] = None
    substyle: Optional[RecraftImageSubStyle] = None
    text_layout: Optional[RecraftTextLayout] = None


class KlingErrorResponse(BaseModel):
    code: int = Field(
        ...,
        description='- 1000: Authentication failed\n- 1001: Authorization is empty\n- 1002: Authorization is invalid\n- 1003: Authorization is not yet valid\n- 1004: Authorization has expired\n- 1100: Account exception\n- 1101: Account in arrears (postpaid scenario)\n- 1102: Resource pack depleted or expired (prepaid scenario)\n- 1103: Unauthorized access to requested resource\n- 1200: Invalid request parameters\n- 1201: Invalid parameters\n- 1202: Invalid request method\n- 1203: Requested resource does not exist\n- 1300: Trigger platform strategy\n- 1301: Trigger content security policy\n- 1302: API request too frequent\n- 1303: Concurrency/QPS exceeds limit\n- 1304: Trigger IP whitelist policy\n- 5000: Internal server error\n- 5001: Service temporarily unavailable\n- 5002: Server internal timeout\n',
    )
    message: str = Field(..., description='Human-readable error message')
    request_id: str = Field(
        ..., description='Request ID for tracking and troubleshooting'
    )


class LumaAspectRatio(str, Enum):
    field_1_1 = '1:1'
    field_16_9 = '16:9'
    field_9_16 = '9:16'
    field_4_3 = '4:3'
    field_3_4 = '3:4'
    field_21_9 = '21:9'
    field_9_21 = '9:21'


class LumaVideoModel(str, Enum):
    ray_2 = 'ray-2'
    ray_flash_2 = 'ray-flash-2'
    ray_1_6 = 'ray-1-6'


class LumaVideoModelOutputResolution1(str, Enum):
    field_540p = '540p'
    field_720p = '720p'
    field_1080p = '1080p'
    field_4k = '4k'


class LumaVideoModelOutputResolution(
    RootModel[Union[LumaVideoModelOutputResolution1, str]]
):
    root: Union[LumaVideoModelOutputResolution1, str]


class LumaVideoModelOutputDuration1(str, Enum):
    field_5s = '5s'
    field_9s = '9s'


class LumaVideoModelOutputDuration(
    RootModel[Union[LumaVideoModelOutputDuration1, str]]
):
    root: Union[LumaVideoModelOutputDuration1, str]


class LumaImageModel(str, Enum):
    photon_1 = 'photon-1'
    photon_flash_1 = 'photon-flash-1'


class LumaImageRef(BaseModel):
    url: Optional[AnyUrl] = Field(None, description='The URL of the image reference')
    weight: Optional[float] = Field(
        None, description='The weight of the image reference'
    )


class LumaImageIdentity(BaseModel):
    images: Optional[List[AnyUrl]] = Field(
        None, description='The URLs of the image identity'
    )


class LumaModifyImageRef(BaseModel):
    url: Optional[AnyUrl] = Field(None, description='The URL of the image reference')
    weight: Optional[float] = Field(
        None, description='The weight of the modify image reference'
    )


class Type1(str, Enum):
    generation = 'generation'


class LumaGenerationReference(BaseModel):
    type: Literal['generation']
    id: UUID = Field(..., description='The ID of the generation')


class Type2(str, Enum):
    image = 'image'


class LumaImageReference(BaseModel):
    type: Literal['image']
    url: AnyUrl = Field(..., description='The URL of the image')


class LumaKeyframe(RootModel[Union[LumaGenerationReference, LumaImageReference]]):
    root: Union[LumaGenerationReference, LumaImageReference] = Field(
        ...,
        description='A keyframe can be either a Generation reference, an Image, or a Video',
        discriminator='type',
    )


class LumaGenerationType(str, Enum):
    video = 'video'
    image = 'image'


class LumaState(str, Enum):
    queued = 'queued'
    dreaming = 'dreaming'
    completed = 'completed'
    failed = 'failed'


class LumaAssets(BaseModel):
    video: Optional[AnyUrl] = Field(None, description='The URL of the video')
    image: Optional[AnyUrl] = Field(None, description='The URL of the image')
    progress_video: Optional[AnyUrl] = Field(
        None, description='The URL of the progress video'
    )


class GenerationType(str, Enum):
    video = 'video'


class GenerationType1(str, Enum):
    image = 'image'


class CharacterRef(BaseModel):
    identity0: Optional[LumaImageIdentity] = None


class LumaImageGenerationRequest(BaseModel):
    generation_type: Optional[GenerationType1] = 'image'
    model: Optional[LumaImageModel] = 'photon-1'
    prompt: Optional[str] = Field(None, description='The prompt of the generation')
    aspect_ratio: Optional[LumaAspectRatio] = '16:9'
    callback_url: Optional[AnyUrl] = Field(
        None, description='The callback URL for the generation'
    )
    image_ref: Optional[List[LumaImageRef]] = None
    style_ref: Optional[List[LumaImageRef]] = None
    character_ref: Optional[CharacterRef] = None
    modify_image_ref: Optional[LumaModifyImageRef] = None


class GenerationType2(str, Enum):
    upscale_video = 'upscale_video'


class LumaUpscaleVideoGenerationRequest(BaseModel):
    generation_type: Optional[GenerationType2] = 'upscale_video'
    resolution: Optional[LumaVideoModelOutputResolution] = None
    callback_url: Optional[AnyUrl] = Field(
        None, description='The callback URL for the upscale'
    )


class GenerationType3(str, Enum):
    add_audio = 'add_audio'


class LumaAudioGenerationRequest(BaseModel):
    generation_type: Optional[GenerationType3] = 'add_audio'
    prompt: Optional[str] = Field(None, description='The prompt of the audio')
    negative_prompt: Optional[str] = Field(
        None, description='The negative prompt of the audio'
    )
    callback_url: Optional[AnyUrl] = Field(
        None, description='The callback URL for the audio'
    )


class LumaError(BaseModel):
    detail: Optional[str] = Field(None, description='The error message')


class AspectRatio(str, Enum):
    field_16_9 = '16:9'
    field_4_3 = '4:3'
    field_1_1 = '1:1'
    field_3_4 = '3:4'
    field_9_16 = '9:16'


class Duration(int, Enum):
    integer_5 = 5
    integer_8 = 8


class Model1(str, Enum):
    v3_5 = 'v3.5'


class MotionMode(str, Enum):
    normal = 'normal'
    fast = 'fast'


class Quality(str, Enum):
    field_360p = '360p'
    field_540p = '540p'
    field_720p = '720p'
    field_1080p = '1080p'


class Style(str, Enum):
    anime = 'anime'
    field_3d_animation = '3d_animation'
    clay = 'clay'
    comic = 'comic'
    cyberpunk = 'cyberpunk'


class PixverseTextVideoRequest(BaseModel):
    aspect_ratio: AspectRatio
    duration: Duration
    model: Model1
    motion_mode: Optional[MotionMode] = None
    negative_prompt: Optional[str] = None
    prompt: str
    quality: Quality
    seed: Optional[int] = None
    style: Optional[Style] = None
    template_id: Optional[int] = None
    water_mark: Optional[bool] = None


class Resp(BaseModel):
    video_id: Optional[int] = None


class PixverseVideoResponse(BaseModel):
    ErrCode: Optional[int] = None
    ErrMsg: Optional[str] = None
    Resp_1: Optional[Resp] = Field(None, alias='Resp')


class Resp1(BaseModel):
    img_id: Optional[int] = None


class PixverseImageUploadResponse(BaseModel):
    ErrCode: Optional[int] = None
    ErrMsg: Optional[str] = None
    Resp: Optional[Resp1] = None


class PixverseImageVideoRequest(BaseModel):
    img_id: int
    model: Model1
    prompt: str
    duration: Duration
    quality: Quality
    motion_mode: Optional[MotionMode] = None
    seed: Optional[int] = None
    style: Optional[Style] = None
    template_id: Optional[int] = None
    water_mark: Optional[bool] = None


class PixverseTransitionVideoRequest(BaseModel):
    first_frame_img: int
    last_frame_img: int
    model: Model1
    duration: Duration
    quality: Quality
    motion_mode: MotionMode
    seed: int
    prompt: str
    style: Optional[Style] = None
    template_id: Optional[int] = None
    water_mark: Optional[bool] = None


class Status2(int, Enum):
    integer_1 = 1
    integer_5 = 5
    integer_6 = 6
    integer_7 = 7
    integer_8 = 8


class Resp2(BaseModel):
    create_time: Optional[str] = None
    id: Optional[int] = None
    modify_time: Optional[str] = None
    negative_prompt: Optional[str] = None
    outputHeight: Optional[int] = None
    outputWidth: Optional[int] = None
    prompt: Optional[str] = None
    resolution_ratio: Optional[int] = None
    seed: Optional[int] = None
    size: Optional[int] = None
    status: Optional[Status2] = Field(
        None,
        description='Video generation status codes:\n* 1 - Generation successful\n* 5 - Generating\n* 6 - Deleted\n* 7 - Contents moderation failed\n* 8 - Generation failed\n',
    )
    style: Optional[str] = None
    url: Optional[str] = None


class PixverseVideoResultResponse(BaseModel):
    ErrCode: Optional[int] = None
    ErrMsg: Optional[str] = None
    Resp: Optional[Resp2] = None


class Image(BaseModel):
    bytesBase64Encoded: str
    gcsUri: Optional[str] = None
    mimeType: Optional[str] = None


class Image1(BaseModel):
    bytesBase64Encoded: Optional[str] = None
    gcsUri: str
    mimeType: Optional[str] = None


class Instance(BaseModel):
    prompt: str = Field(..., description='Text description of the video')
    image: Optional[Union[Image, Image1]] = Field(
        None, description='Optional image to guide video generation'
    )


class PersonGeneration(str, Enum):
    ALLOW = 'ALLOW'
    BLOCK = 'BLOCK'


class Parameters(BaseModel):
    aspectRatio: Optional[str] = Field(None, examples=['16:9'])
    negativePrompt: Optional[str] = None
    personGeneration: Optional[PersonGeneration] = None
    sampleCount: Optional[int] = None
    seed: Optional[int] = None
    storageUri: Optional[str] = Field(
        None, description='Optional Cloud Storage URI to upload the video'
    )
    durationSeconds: Optional[int] = None
    enhancePrompt: Optional[bool] = None


class Veo2GenVidRequest(BaseModel):
    instances: Optional[List[Instance]] = None
    parameters: Optional[Parameters] = None


class Veo2GenVidResponse(BaseModel):
    name: str = Field(
        ...,
        description='Operation resource name',
        examples=[
            'projects/PROJECT_ID/locations/us-central1/publishers/google/models/MODEL_ID/operations/a1b07c8e-7b5a-4aba-bb34-3e1ccb8afcc8'
        ],
    )


class Veo2GenVidPollRequest(BaseModel):
    operationName: str = Field(
        ...,
        description='Full operation name (from predict response)',
        examples=[
            'projects/PROJECT_ID/locations/us-central1/publishers/google/models/MODEL_ID/operations/OPERATION_ID'
        ],
    )


class Video(BaseModel):
    gcsUri: Optional[str] = Field(None, description='Cloud Storage URI of the video')
    bytesBase64Encoded: Optional[str] = Field(
        None, description='Base64-encoded video content'
    )
    mimeType: Optional[str] = Field(None, description='Video MIME type')


class Response(BaseModel):
    field_type: Optional[str] = Field(
        None,
        alias='@type',
        examples=[
            'type.googleapis.com/cloud.ai.large_models.vision.GenerateVideoResponse'
        ],
    )
    raiMediaFilteredCount: Optional[int] = Field(
        None, description='Count of media filtered by responsible AI policies'
    )
    raiMediaFilteredReasons: Optional[List[str]] = Field(
        None, description='Reasons why media was filtered by responsible AI policies'
    )
    videos: Optional[List[Video]] = None


class Error1(BaseModel):
    code: Optional[int] = Field(None, description='Error code')
    message: Optional[str] = Field(None, description='Error message')


class Veo2GenVidPollResponse(BaseModel):
    name: Optional[str] = None
    done: Optional[bool] = None
    response: Optional[Response] = Field(
        None, description='The actual prediction response if done is true'
    )
    error: Optional[Error1] = Field(
        None, description='Error details if operation failed'
    )


class RunwayImageToVideoResponse(BaseModel):
    id: Optional[str] = Field(None, description='Task ID')


class RunwayTaskStatusEnum(str, Enum):
    SUCCEEDED = 'SUCCEEDED'
    RUNNING = 'RUNNING'
    FAILED = 'FAILED'
    PENDING = 'PENDING'
    CANCELLED = 'CANCELLED'
    THROTTLED = 'THROTTLED'


class RunwayModelEnum(str, Enum):
    gen4_turbo = 'gen4_turbo'
    gen3a_turbo = 'gen3a_turbo'


class Position(str, Enum):
    first = 'first'
    last = 'last'


class RunwayPromptImageDetailedObject(BaseModel):
    uri: str = Field(
        ..., description='A HTTPS URL or data URI containing an encoded image.'
    )
    position: Position = Field(
        ...,
        description="The position of the image in the output video. 'last' is currently supported for gen3a_turbo only.",
    )


class RunwayDurationEnum(int, Enum):
    integer_5 = 5
    integer_10 = 10


class RunwayAspectRatioEnum(str, Enum):
    field_1280_720 = '1280:720'
    field_720_1280 = '720:1280'
    field_1104_832 = '1104:832'
    field_832_1104 = '832:1104'
    field_960_960 = '960:960'
    field_1584_672 = '1584:672'
    field_1280_768 = '1280:768'
    field_768_1280 = '768:1280'


class RunwayPromptImageObject(
    RootModel[Union[str, List[RunwayPromptImageDetailedObject]]]
):
    root: Union[str, List[RunwayPromptImageDetailedObject]] = Field(
        ...,
        description='Image(s) to use for the video generation. Can be a single URI or an array of image objects with positions.',
    )


class Datum3(BaseModel):
    b64_json: Optional[str] = Field(None, description='Base64 encoded image data')
    url: Optional[str] = Field(None, description='URL of the image')
    revised_prompt: Optional[str] = Field(None, description='Revised prompt')


class InputTokensDetails(BaseModel):
    text_tokens: Optional[int] = None
    image_tokens: Optional[int] = None


class Usage(BaseModel):
    input_tokens: Optional[int] = None
    input_tokens_details: Optional[InputTokensDetails] = None
    output_tokens: Optional[int] = None
    total_tokens: Optional[int] = None


class OpenAIImageGenerationResponse(BaseModel):
    data: Optional[List[Datum3]] = None
    usage: Optional[Usage] = None


class Quality3(str, Enum):
    low = 'low'
    medium = 'medium'
    high = 'high'
    standard = 'standard'
    hd = 'hd'


class OutputFormat1(str, Enum):
    png = 'png'
    webp = 'webp'
    jpeg = 'jpeg'


class Moderation(str, Enum):
    low = 'low'
    auto = 'auto'


class Background(str, Enum):
    transparent = 'transparent'
    opaque = 'opaque'


class ResponseFormat(str, Enum):
    url = 'url'
    b64_json = 'b64_json'


class Style3(str, Enum):
    vivid = 'vivid'
    natural = 'natural'


class OpenAIImageGenerationRequest(BaseModel):
    model: Optional[str] = Field(
        None, description='The model to use for image generation', examples=['dall-e-3']
    )
    prompt: str = Field(
        ...,
        description='A text description of the desired image',
        examples=['Draw a rocket in front of a blackhole in deep space'],
    )
    n: Optional[int] = Field(
        None,
        description='The number of images to generate (1-10). Only 1 supported for dall-e-3.',
        examples=[1],
    )
    quality: Optional[Quality3] = Field(
        None, description='The quality of the generated image', examples=['high']
    )
    size: Optional[str] = Field(
        None,
        description='Size of the image (e.g., 1024x1024, 1536x1024, auto)',
        examples=['1024x1536'],
    )
    output_format: Optional[OutputFormat1] = Field(
        None, description='Format of the output image', examples=['png']
    )
    output_compression: Optional[int] = Field(
        None, description='Compression level for JPEG or WebP (0-100)', examples=[100]
    )
    moderation: Optional[Moderation] = Field(
        None, description='Content moderation setting', examples=['auto']
    )
    background: Optional[Background] = Field(
        None, description='Background transparency', examples=['opaque']
    )
    response_format: Optional[ResponseFormat] = Field(
        None, description='Response format of image data', examples=['b64_json']
    )
    style: Optional[Style3] = Field(
        None, description='Style of the image (only for dall-e-3)', examples=['vivid']
    )
    user: Optional[str] = Field(
        None,
        description='A unique identifier for end-user monitoring',
        examples=['user-1234'],
    )


class OpenAIImageEditRequest(BaseModel):
    model: str = Field(
        ..., description='The model to use for image editing', examples=['gpt-image-1']
    )
    prompt: str = Field(
        ...,
        description='A text description of the desired edit',
        examples=['Give the rocketship rainbow coloring'],
    )
    n: Optional[int] = Field(
        None, description='The number of images to generate', examples=[1]
    )
    quality: Optional[str] = Field(
        None, description='The quality of the edited image', examples=['low']
    )
    size: Optional[str] = Field(
        None, description='Size of the output image', examples=['1024x1024']
    )
    output_format: Optional[OutputFormat1] = Field(
        None, description='Format of the output image', examples=['png']
    )
    output_compression: Optional[int] = Field(
        None, description='Compression level for JPEG or WebP (0-100)', examples=[100]
    )
    moderation: Optional[Moderation] = Field(
        None, description='Content moderation setting', examples=['auto']
    )
    background: Optional[str] = Field(
        None, description='Background transparency', examples=['opaque']
    )
    user: Optional[str] = Field(
        None,
        description='A unique identifier for end-user monitoring',
        examples=['user-1234'],
    )


class CustomerStorageResourceResponse(BaseModel):
    download_url: Optional[str] = Field(
        None,
        description='The signed URL to use for downloading the file from the specified path',
    )
    upload_url: Optional[str] = Field(
        None,
        description='The signed URL to use for uploading the file to the specified path',
    )
    expires_at: Optional[datetime] = Field(
        None, description='When the signed URL will expire'
    )
    existing_file: Optional[bool] = Field(
        None, description='Whether an existing file with the same hash was found'
    )


class Pikaffect(str, Enum):
    Cake_ify = 'Cake-ify'
    Crumble = 'Crumble'
    Crush = 'Crush'
    Decapitate = 'Decapitate'
    Deflate = 'Deflate'
    Dissolve = 'Dissolve'
    Explode = 'Explode'
    Eye_pop = 'Eye-pop'
    Inflate = 'Inflate'
    Levitate = 'Levitate'
    Melt = 'Melt'
    Peel = 'Peel'
    Poke = 'Poke'
    Squish = 'Squish'
    Ta_da = 'Ta-da'
    Tear = 'Tear'


class PikaBodyGeneratePikaffectsGeneratePikaffectsPost(BaseModel):
    image: Optional[StrictBytes] = Field(None, title='Image')
    pikaffect: Optional[Pikaffect] = Field(None, title='Pikaffect')
    promptText: Optional[str] = Field(None, title='Prompttext')
    negativePrompt: Optional[str] = Field(None, title='Negativeprompt')
    seed: Optional[int] = Field(None, title='Seed')


class PikaGenerateResponse(BaseModel):
    video_id: str = Field(..., title='Video Id')


class PikaBodyGeneratePikadditionsGeneratePikadditionsPost(BaseModel):
    video: Optional[StrictBytes] = Field(None, title='Video')
    image: Optional[StrictBytes] = Field(None, title='Image')
    promptText: Optional[str] = Field(None, title='Prompttext')
    negativePrompt: Optional[str] = Field(None, title='Negativeprompt')
    seed: Optional[int] = Field(None, title='Seed')


class PikaBodyGeneratePikaswapsGeneratePikaswapsPost(BaseModel):
    video: Optional[StrictBytes] = Field(None, title='Video')
    image: Optional[StrictBytes] = Field(None, title='Image')
    promptText: Optional[str] = Field(None, title='Prompttext')
    modifyRegionMask: Optional[StrictBytes] = Field(
        None,
        description='A mask image that specifies the region to modify, where the mask is white and the background is black',
        title='Modifyregionmask',
    )
    modifyRegionRoi: Optional[str] = Field(
        None,
        description='Plaintext description of the object / region to modify',
        title='Modifyregionroi',
    )
    negativePrompt: Optional[str] = Field(None, title='Negativeprompt')
    seed: Optional[int] = Field(None, title='Seed')


class IngredientsMode(str, Enum):
    creative = 'creative'
    precise = 'precise'


class AspectRatio1(RootModel[float]):
    root: float = Field(
        ...,
        description='Aspect ratio (width / height)',
        ge=0.4,
        le=2.5,
        title='Aspectratio',
    )


class PikaBodyGenerate22C2vGenerate22PikascenesPost(BaseModel):
    images: Optional[List[StrictBytes]] = Field(None, title='Images')
    ingredientsMode: IngredientsMode = Field(..., title='Ingredientsmode')
    promptText: Optional[str] = Field(None, title='Prompttext')
    negativePrompt: Optional[str] = Field(None, title='Negativeprompt')
    seed: Optional[int] = Field(None, title='Seed')
    resolution: Optional[str] = Field('1080p', title='Resolution')
    duration: Optional[int] = Field(5, title='Duration')
    aspectRatio: Optional[AspectRatio1] = Field(
        None, description='Aspect ratio (width / height)', title='Aspectratio'
    )


class PikaStatusEnum(str, Enum):
    queued = 'queued'
    started = 'started'
    finished = 'finished'


class PikaValidationError(BaseModel):
    loc: List[Union[str, int]] = Field(..., title='Location')
    msg: str = Field(..., title='Message')
    type: str = Field(..., title='Error Type')


class PikaResolutionEnum(str, Enum):
    field_1080p = '1080p'
    field_720p = '720p'


class PikaDurationEnum(int, Enum):
    integer_5 = 5
    integer_10 = 10


class RgbItem(RootModel[int]):
    root: int = Field(..., ge=0, le=255)


class RGBColor(BaseModel):
    rgb: List[RgbItem] = Field(..., max_length=3, min_length=3)


class StabilityStabilityClientID(RootModel[str]):
    root: str = Field(
        ...,
        description='The name of your application, used to help us communicate app-specific debugging or moderation issues to you.',
        examples=['my-awesome-app'],
        max_length=256,
    )


class StabilityStabilityClientUserID(RootModel[str]):
    root: str = Field(
        ...,
        description='A unique identifier for your end user. Used to help us communicate user-specific debugging or moderation issues to you. Feel free to obfuscate this value to protect user privacy.',
        examples=['DiscordUser#9999'],
        max_length=256,
    )


class StabilityStabilityClientVersion(RootModel[str]):
    root: str = Field(
        ...,
        description='The version of your application, used to help us communicate version-specific debugging or moderation issues to you.',
        examples=['1.2.1'],
        max_length=256,
    )


class Name(str, Enum):
    content_moderation = 'content_moderation'


class StabilityContentModerationResponse(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new) you file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: Name = Field(
        ...,
        description='Our content moderation system has flagged some part of your request and subsequently denied it.  You were not charged for this request.  While this may at times be frustrating, it is necessary to maintain the integrity of our platform and ensure a safe experience for all users. If you would like to provide feedback, please use the [Support Form](https://kb.stability.ai/knowledge-base/kb-tickets/new).',
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class RenderingSpeed(str, Enum):
    BALANCED = 'BALANCED'
    TURBO = 'TURBO'
    QUALITY = 'QUALITY'


class StabilityCreativity(RootModel[float]):
    root: float = Field(
        ...,
        description='Controls the likelihood of creating additional details not heavily conditioned by the init image.',
        ge=0.2,
        le=0.5,
    )


class StabilityGenerationID(RootModel[str]):
    root: str = Field(
        ...,
        description='The `id` of a generation, typically used for async generations, that can be used to check the status of the generation or retrieve the result.',
        examples=['a6dc6c6e20acda010fe14d71f180658f2896ed9b4ec25aa99a6ff06c796987c4'],
        max_length=64,
        min_length=64,
    )


class Mode(str, Enum):
    text_to_image = 'text-to-image'
    image_to_image = 'image-to-image'


class AspectRatio2(str, Enum):
    field_21_9 = '21:9'
    field_16_9 = '16:9'
    field_3_2 = '3:2'
    field_5_4 = '5:4'
    field_1_1 = '1:1'
    field_4_5 = '4:5'
    field_2_3 = '2:3'
    field_9_16 = '9:16'
    field_9_21 = '9:21'


class Model4(str, Enum):
    sd3_5_large = 'sd3.5-large'
    sd3_5_large_turbo = 'sd3.5-large-turbo'
    sd3_5_medium = 'sd3.5-medium'


class OutputFormat3(str, Enum):
    png = 'png'
    jpeg = 'jpeg'


class StylePreset(str, Enum):
    enhance = 'enhance'
    anime = 'anime'
    photographic = 'photographic'
    digital_art = 'digital-art'
    comic_book = 'comic-book'
    fantasy_art = 'fantasy-art'
    line_art = 'line-art'
    analog_film = 'analog-film'
    neon_punk = 'neon-punk'
    isometric = 'isometric'
    low_poly = 'low-poly'
    origami = 'origami'
    modeling_compound = 'modeling-compound'
    cinematic = 'cinematic'
    field_3d_model = '3d-model'
    pixel_art = 'pixel-art'
    tile_texture = 'tile-texture'


class StabilityImageGenrationSD3Request(BaseModel):
    prompt: str = Field(
        ...,
        description='What you wish to see in the output image. A strong, descriptive prompt that clearly defines\nelements, colors, and subjects will lead to better results.',
        max_length=10000,
        min_length=1,
    )
    mode: Optional[Mode] = Field(
        'text-to-image',
        description='Controls whether this is a text-to-image or image-to-image generation, which affects which parameters are required:\n- **text-to-image** requires only the `prompt` parameter\n- **image-to-image** requires the `prompt`, `image`, and `strength` parameters',
        title='GenerationMode',
    )
    image: Optional[StrictBytes] = Field(
        None,
        description='The image to use as the starting point for the generation.\n\nSupported formats:\n\n\n\n  - jpeg\n  - png\n  - webp\n\nSupported dimensions:\n\n\n\n  - Every side must be at least 64 pixels\n\n> **Important:** This parameter is only valid for **image-to-image** requests.',
    )
    strength: Optional[float] = Field(
        None,
        description='Sometimes referred to as _denoising_, this parameter controls how much influence the\n`image` parameter has on the generated image.  A value of 0 would yield an image that\nis identical to the input.  A value of 1 would be as if you passed in no image at all.\n\n> **Important:** This parameter is only valid for **image-to-image** requests.',
        ge=0.0,
        le=1.0,
    )
    aspect_ratio: Optional[AspectRatio2] = Field(
        '1:1',
        description='Controls the aspect ratio of the generated image. Defaults to 1:1.\n\n> **Important:** This parameter is only valid for **text-to-image** requests.',
    )
    model: Optional[Model4] = Field(
        'sd3.5-large',
        description='The model to use for generation.\n\n- `sd3.5-large` requires 6.5 credits per generation\n- `sd3.5-large-turbo` requires 4 credits per generation\n- `sd3.5-medium` requires 3.5 credits per generation\n- As of the April 17, 2025, `sd3-large`, `sd3-large-turbo` and `sd3-medium`\n\n\n\n  are re-routed to their `sd3.5-[model version]` equivalent, at the same price.',
    )
    seed: Optional[float] = Field(
        0,
        description="A specific value that is used to guide the 'randomness' of the generation. (Omit this parameter or pass `0` to use a random seed.)",
        ge=0.0,
        le=4294967294.0,
    )
    output_format: Optional[OutputFormat3] = Field(
        'png', description='Dictates the `content-type` of the generated image.'
    )
    style_preset: Optional[StylePreset] = Field(
        None, description='Guides the image model towards a particular style.'
    )
    negative_prompt: Optional[str] = Field(
        None,
        description='Keywords of what you **do not** wish to see in the output image.\nThis is an advanced feature.',
        max_length=10000,
    )
    cfg_scale: Optional[float] = Field(
        None,
        description='How strictly the diffusion process adheres to the prompt text (higher values keep your image closer to your prompt). The _Large_ and _Medium_ models use a default of `4`. The _Turbo_ model uses a default of `1`.',
        ge=1.0,
        le=10.0,
    )


class FinishReason(str, Enum):
    SUCCESS = 'SUCCESS'
    CONTENT_FILTERED = 'CONTENT_FILTERED'


class StabilityImageGenrationSD3Response200(BaseModel):
    image: str = Field(
        ...,
        description='The generated image, encoded to base64.',
        examples=['AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1...'],
    )
    seed: Optional[float] = Field(
        0,
        description='The seed used as random noise for this generation.',
        examples=[343940597],
        ge=0.0,
        le=4294967294.0,
    )
    finish_reason: FinishReason = Field(
        ...,
        description='The reason the generation finished.\n\n- `SUCCESS` = successful generation.\n- `CONTENT_FILTERED` = successful generation, however the output violated our content moderation\npolicy and has been blurred as a result.',
        examples=['SUCCESS'],
    )


class StabilityImageGenrationSD3Response400(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationSD3Response413(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationSD3Response422(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationSD3Response429(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationSD3Response500(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class OutputFormat4(str, Enum):
    jpeg = 'jpeg'
    png = 'png'
    webp = 'webp'


class StabilityImageGenrationUpscaleConservativeRequest(BaseModel):
    image: StrictBytes = Field(
        ...,
        description='The image you wish to upscale.\n\nSupported Formats:\n- jpeg\n- png\n- webp\n\nValidation Rules:\n- Every side must be at least 64 pixels\n- Total pixel count must be between 4,096 and 9,437,184 pixels\n- The aspect ratio must be between 1:2.5 and 2.5:1',
        examples=['./some/image.png'],
    )
    prompt: str = Field(
        ...,
        description="What you wish to see in the output image. A strong, descriptive prompt that clearly defines\nelements, colors, and subjects will lead to better results.\n\nTo control the weight of a given word use the format `(word:weight)`,\nwhere `word` is the word you'd like to control the weight of and `weight`\nis a value between 0 and 1. For example: `The sky was a crisp (blue:0.3) and (green:0.8)`\nwould convey a sky that was blue and green, but more green than blue.",
        max_length=10000,
        min_length=1,
    )
    negative_prompt: Optional[str] = Field(
        None,
        description='A blurb of text describing what you **do not** wish to see in the output image.\nThis is an advanced feature.',
        max_length=10000,
    )
    seed: Optional[float] = Field(
        0,
        description="A specific value that is used to guide the 'randomness' of the generation. (Omit this parameter or pass `0` to use a random seed.)",
        ge=0.0,
        le=4294967294.0,
    )
    output_format: Optional[OutputFormat4] = Field(
        'png', description='Dictates the `content-type` of the generated image.'
    )
    creativity: Optional[StabilityCreativity] = Field(
        default_factory=lambda: StabilityCreativity.model_validate(0.35)
    )


class StabilityImageGenrationUpscaleConservativeResponse200(BaseModel):
    image: str = Field(
        ...,
        description='The generated image, encoded to base64.',
        examples=['AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1...'],
    )
    seed: Optional[float] = Field(
        0,
        description='The seed used as random noise for this generation.',
        examples=[343940597],
        ge=0.0,
        le=4294967294.0,
    )
    finish_reason: FinishReason = Field(
        ...,
        description='The reason the generation finished.\n\n- `SUCCESS` = successful generation.\n- `CONTENT_FILTERED` = successful generation, however the output violated our content moderation\npolicy and has been blurred as a result.',
        examples=['SUCCESS'],
    )


class StabilityImageGenrationUpscaleConservativeResponse400(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationUpscaleConservativeResponse413(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationUpscaleConservativeResponse422(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationUpscaleConservativeResponse429(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationUpscaleConservativeResponse500(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationUpscaleCreativeRequest(BaseModel):
    image: StrictBytes = Field(
        ...,
        description='The image you wish to upscale.\n\nSupported Formats:\n- jpeg\n- png\n- webp\n\nValidation Rules:\n- Every side must be at least 64 pixels\n- Total pixel count must be between 4,096 and 1,048,576 pixels',
        examples=['./some/image.png'],
    )
    prompt: str = Field(
        ...,
        description="What you wish to see in the output image. A strong, descriptive prompt that clearly defines\nelements, colors, and subjects will lead to better results.\n\nTo control the weight of a given word use the format `(word:weight)`,\nwhere `word` is the word you'd like to control the weight of and `weight`\nis a value between 0 and 1. For example: `The sky was a crisp (blue:0.3) and (green:0.8)`\nwould convey a sky that was blue and green, but more green than blue.",
        max_length=10000,
        min_length=1,
    )
    negative_prompt: Optional[str] = Field(
        None,
        description='A blurb of text describing what you **do not** wish to see in the output image.\nThis is an advanced feature.',
        max_length=10000,
    )
    output_format: Optional[OutputFormat4] = Field(
        'png', description='Dictates the `content-type` of the generated image.'
    )
    seed: Optional[float] = Field(
        0,
        description="A specific value that is used to guide the 'randomness' of the generation. (Omit this parameter or pass `0` to use a random seed.)",
        ge=0.0,
        le=4294967294.0,
    )
    creativity: Optional[float] = Field(
        0.3,
        description='Indicates how creative the model should be when upscaling an image.\nHigher values will result in more details being added to the image during upscaling.',
        ge=0.1,
        le=0.5,
    )
    style_preset: Optional[StylePreset] = Field(
        None, description='Guides the image model towards a particular style.'
    )


class StabilityImageGenrationUpscaleCreativeResponse200(BaseModel):
    id: StabilityGenerationID


class StabilityImageGenrationUpscaleCreativeResponse400(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationUpscaleCreativeResponse413(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationUpscaleCreativeResponse422(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationUpscaleCreativeResponse429(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationUpscaleCreativeResponse500(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationUpscaleFastRequest(BaseModel):
    image: StrictBytes = Field(
        ...,
        description='The image you wish to upscale.\n\nSupported Formats:\n- jpeg\n- png\n- webp\n\nValidation Rules:\n- Width must be between 32 and 1,536 pixels\n- Height must be between 32 and 1,536 pixels\n- Total pixel count must be between 1,024 and 1,048,576 pixels',
        examples=['./some/image.png'],
    )
    output_format: Optional[OutputFormat4] = Field(
        'png', description='Dictates the `content-type` of the generated image.'
    )


class StabilityImageGenrationUpscaleFastResponse200(BaseModel):
    image: str = Field(
        ...,
        description='The generated image, encoded to base64.',
        examples=['AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1...'],
    )
    seed: Optional[float] = Field(
        0,
        description='The seed used as random noise for this generation.',
        examples=[343940597],
        ge=0.0,
        le=4294967294.0,
    )
    finish_reason: FinishReason = Field(
        ...,
        description='The reason the generation finished.\n\n- `SUCCESS` = successful generation.\n- `CONTENT_FILTERED` = successful generation, however the output violated our content moderation\npolicy and has been blurred as a result.',
        examples=['SUCCESS'],
    )


class StabilityImageGenrationUpscaleFastResponse400(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationUpscaleFastResponse413(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationUpscaleFastResponse422(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationUpscaleFastResponse429(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class StabilityImageGenrationUpscaleFastResponse500(BaseModel):
    id: str = Field(
        ...,
        description='A unique identifier associated with this error. Please include this in any [support tickets](https://kb.stability.ai/knowledge-base/kb-tickets/new)\nyou file, as it will greatly assist us in diagnosing the root cause of the problem.',
        examples=['a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4'],
        min_length=1,
    )
    name: str = Field(
        ...,
        description='Short-hand name for an error, useful for discriminating between errors with the same status code.',
        examples=['bad_request'],
        min_length=1,
    )
    errors: List[str] = Field(
        ...,
        description='One or more error messages indicating what went wrong.',
        examples=[['some-field: is required']],
        min_length=1,
    )


class ActionJobResult(BaseModel):
    id: Optional[UUID] = Field(None, description='Unique identifier for the job result')
    workflow_name: Optional[str] = Field(None, description='Name of the workflow')
    operating_system: Optional[str] = Field(None, description='Operating system used')
    python_version: Optional[str] = Field(None, description='PyTorch version used')
    pytorch_version: Optional[str] = Field(None, description='PyTorch version used')
    action_run_id: Optional[str] = Field(
        None, description='Identifier of the run this result belongs to'
    )
    action_job_id: Optional[str] = Field(
        None, description='Identifier of the job this result belongs to'
    )
    cuda_version: Optional[str] = Field(None, description='CUDA version used')
    branch_name: Optional[str] = Field(
        None, description='Name of the relevant git branch'
    )
    commit_hash: Optional[str] = Field(None, description='The hash of the commit')
    commit_id: Optional[str] = Field(None, description='The ID of the commit')
    commit_time: Optional[int] = Field(
        None, description='The Unix timestamp when the commit was made'
    )
    commit_message: Optional[str] = Field(None, description='The message of the commit')
    comfy_run_flags: Optional[str] = Field(
        None, description='The comfy run flags. E.g. `--low-vram`'
    )
    git_repo: Optional[str] = Field(None, description='The repository name')
    pr_number: Optional[str] = Field(None, description='The pull request number')
    start_time: Optional[int] = Field(
        None, description='The start time of the job as a Unix timestamp.'
    )
    end_time: Optional[int] = Field(
        None, description='The end time of the job as a Unix timestamp.'
    )
    avg_vram: Optional[int] = Field(
        None, description='The average VRAM used by the job'
    )
    peak_vram: Optional[int] = Field(None, description='The peak VRAM used by the job')
    job_trigger_user: Optional[str] = Field(
        None, description='The user who triggered the job.'
    )
    author: Optional[str] = Field(None, description='The author of the commit')
    machine_stats: Optional[MachineStats] = None
    status: Optional[WorkflowRunStatus] = None
    storage_file: Optional[StorageFile] = None


class Publisher(BaseModel):
    name: Optional[str] = None
    id: Optional[str] = Field(
        None,
        description="The unique identifier for the publisher. It's akin to a username. Should be lowercase.",
    )
    description: Optional[str] = None
    website: Optional[str] = None
    support: Optional[str] = None
    source_code_repo: Optional[str] = None
    logo: Optional[str] = Field(None, description="URL to the publisher's logo.")
    createdAt: Optional[datetime] = Field(
        None, description='The date and time the publisher was created.'
    )
    members: Optional[List[PublisherMember]] = Field(
        None, description='A list of members in the publisher.'
    )
    status: Optional[PublisherStatus] = Field(
        None, description='The status of the publisher.'
    )


class NodeVersion(BaseModel):
    id: Optional[str] = None
    version: Optional[str] = Field(
        None,
        description='The version identifier, following semantic versioning. Must be unique for the node.',
    )
    createdAt: Optional[datetime] = Field(
        None, description='The date and time the version was created.'
    )
    changelog: Optional[str] = Field(
        None, description='Summary of changes made in this version'
    )
    dependencies: Optional[List[str]] = Field(
        None, description='A list of pip dependencies required by the node.'
    )
    downloadUrl: Optional[str] = Field(
        None, description='[Output Only] URL to download this version of the node'
    )
    deprecated: Optional[bool] = Field(
        None, description='Indicates if this version is deprecated.'
    )
    status: Optional[NodeVersionStatus] = Field(
        None, description='The status of the node version.'
    )
    status_reason: Optional[str] = Field(
        None, description='The reason for the status change.'
    )
    node_id: Optional[str] = Field(
        None, description='The unique identifier of the node.'
    )
    comfy_node_extract_status: Optional[str] = Field(
        None, description='The status of comfy node extraction process.'
    )


class IdeogramV3Request(BaseModel):
    prompt: str = Field(..., description='The text prompt for image generation')
    seed: Optional[int] = Field(
        None, description='Seed value for reproducible generation'
    )
    resolution: Optional[str] = Field(
        None, description='Image resolution in format WxH', examples=['1280x800']
    )
    aspect_ratio: Optional[str] = Field(
        None, description='Aspect ratio in format WxH', examples=['1x3']
    )
    rendering_speed: RenderingSpeed
    magic_prompt: Optional[MagicPrompt] = Field(
        None, description='Whether to enable magic prompt enhancement'
    )
    negative_prompt: Optional[str] = Field(
        None, description='Text prompt specifying what to avoid in the generation'
    )
    num_images: Optional[int] = Field(
        None, description='Number of images to generate', ge=1
    )
    color_palette: Optional[ColorPalette] = None
    style_codes: Optional[List[StyleCode]] = Field(
        None, description='Array of style codes in hexadecimal format'
    )
    style_type: Optional[StyleType] = Field(
        None, description='The type of style to apply'
    )
    style_reference_images: Optional[List[str]] = Field(
        None, description='Array of reference image URLs or identifiers'
    )


class IdeogramV3EditRequest(BaseModel):
    image: Optional[StrictBytes] = Field(
        None,
        description='The image being edited (max size 10MB); only JPEG, WebP and PNG formats are supported at this time.',
    )
    mask: Optional[StrictBytes] = Field(
        None,
        description='A black and white image of the same size as the image being edited (max size 10MB). Black regions in the mask should match up with the regions of the image that you would like to edit; only JPEG, WebP and PNG formats are supported at this time.',
    )
    prompt: str = Field(
        ..., description='The prompt used to describe the edited result.'
    )
    magic_prompt: Optional[str] = Field(
        None,
        description='Determine if MagicPrompt should be used in generating the request or not.',
    )
    num_images: Optional[int] = Field(
        None, description='The number of images to generate.'
    )
    seed: Optional[int] = Field(
        None, description='Random seed. Set for reproducible generation.'
    )
    rendering_speed: RenderingSpeed
    color_palette: Optional[IdeogramColorPalette] = Field(
        None,
        description='A color palette for generation, must EITHER be specified via one of the presets (name) or explicitly via hexadecimal representations of the color with optional weights (members). Not supported by V_1, V_1_TURBO, V_2A and V_2A_TURBO models.',
    )
    style_codes: Optional[List[StyleCode]] = Field(
        None,
        description='A list of 8 character hexadecimal codes representing the style of the image. Cannot be used in conjunction with style_reference_images or style_type.',
    )
    style_reference_images: Optional[List[StrictBytes]] = Field(
        None,
        description='A set of images to use as style references (maximum total size 10MB across all style references). The images should be in JPEG, PNG or WebP format.',
    )


class KlingCameraControl(BaseModel):
    type: Optional[KlingCameraControlType] = None
    config: Optional[KlingCameraConfig] = None


class KlingText2VideoRequest(BaseModel):
    model_name: Optional[KlingVideoGenModelName] = 'kling-v2-master'
    prompt: Optional[str] = Field(
        None, description='Positive text prompt', max_length=2500
    )
    negative_prompt: Optional[str] = Field(
        None, description='Negative text prompt', max_length=2500
    )
    cfg_scale: Optional[KlingVideoGenCfgScale] = Field(
        default_factory=lambda: KlingVideoGenCfgScale.model_validate(0.5)
    )
    mode: Optional[KlingVideoGenMode] = 'std'
    camera_control: Optional[KlingCameraControl] = None
    aspect_ratio: Optional[KlingVideoGenAspectRatio] = '16:9'
    duration: Optional[KlingVideoGenDuration] = '5'
    callback_url: Optional[AnyUrl] = Field(
        None, description='The callback notification address'
    )
    external_task_id: Optional[str] = Field(None, description='Customized Task ID')


class KlingImage2VideoRequest(BaseModel):
    model_name: Optional[KlingVideoGenModelName] = 'kling-v2-master'
    image: Optional[str] = Field(
        None,
        description='Reference Image - URL or Base64 encoded string, cannot exceed 10MB, resolution not less than 300*300px, aspect ratio between 1:2.5 ~ 2.5:1. Base64 should not include data:image prefix.',
    )
    image_tail: Optional[str] = Field(
        None,
        description='Reference Image - End frame control. URL or Base64 encoded string, cannot exceed 10MB, resolution not less than 300*300px. Base64 should not include data:image prefix.',
    )
    prompt: Optional[str] = Field(
        None, description='Positive text prompt', max_length=2500
    )
    negative_prompt: Optional[str] = Field(
        None, description='Negative text prompt', max_length=2500
    )
    cfg_scale: Optional[KlingVideoGenCfgScale] = Field(
        default_factory=lambda: KlingVideoGenCfgScale.model_validate(0.5)
    )
    mode: Optional[KlingVideoGenMode] = 'std'
    static_mask: Optional[str] = Field(
        None,
        description='Static Brush Application Area (Mask image created by users using the motion brush). The aspect ratio must match the input image.',
    )
    dynamic_masks: Optional[List[DynamicMask]] = Field(
        None,
        description='Dynamic Brush Configuration List (up to 6 groups). For 5-second videos, trajectory length must not exceed 77 coordinates.',
    )
    camera_control: Optional[KlingCameraControl] = None
    aspect_ratio: Optional[KlingVideoGenAspectRatio] = '16:9'
    duration: Optional[KlingVideoGenDuration] = '5'
    callback_url: Optional[AnyUrl] = Field(
        None,
        description='The callback notification address. Server will notify when the task status changes.',
    )
    external_task_id: Optional[str] = Field(
        None,
        description='Customized Task ID. Must be unique within a single user account.',
    )


class KlingVideoEffectsInput(
    RootModel[Union[KlingSingleImageEffectInput, KlingDualCharacterEffectInput]]
):
    root: Union[KlingSingleImageEffectInput, KlingDualCharacterEffectInput]


class StripeBillingDetails(BaseModel):
    address: Optional[StripeAddress] = None
    email: Optional[str] = None
    name: Optional[str] = None
    phone: Optional[str] = None
    tax_id: Optional[Any] = None


class StripePaymentMethodDetails(BaseModel):
    card: Optional[StripeCardDetails] = None
    type: Optional[str] = None


class BFLFluxProFillInputs(BaseModel):
    image: str = Field(
        ...,
        description='A Base64-encoded string representing the image you wish to modify. Can contain alpha mask if desired.',
        title='Image',
    )
    mask: Optional[str] = Field(
        None,
        description='A Base64-encoded string representing a mask for the areas you want to modify in the image. The mask should be the same dimensions as the image and in black and white. Black areas (0%) indicate no modification, while white areas (100%) specify areas for inpainting. Optional if you provide an alpha mask in the original image. Validation: The endpoint verifies that the dimensions of the mask match the original image.',
        title='Mask',
    )
    prompt: Optional[str] = Field(
        '',
        description='The description of the changes you want to make. This text guides the inpainting process, allowing you to specify features, styles, or modifications for the masked area.',
        examples=['ein fantastisches bild'],
        title='Prompt',
    )
    steps: Optional[Steps] = Field(
        default_factory=lambda: Steps.model_validate(50),
        description='Number of steps for the image generation process',
        examples=[50],
        title='Steps',
    )
    prompt_upsampling: Optional[bool] = Field(
        False,
        description='Whether to perform upsampling on the prompt. If active, automatically modifies the prompt for more creative generation',
        title='Prompt Upsampling',
    )
    seed: Optional[int] = Field(
        None, description='Optional seed for reproducibility', title='Seed'
    )
    guidance: Optional[Guidance] = Field(
        default_factory=lambda: Guidance.model_validate(60),
        description='Guidance strength for the image generation process',
        title='Guidance',
    )
    output_format: Optional[BFLOutputFormat] = Field(
        'jpeg',
        description="Output format for the generated image. Can be 'jpeg' or 'png'.",
    )
    safety_tolerance: Optional[int] = Field(
        2,
        description='Tolerance level for input and output moderation. Between 0 and 6, 0 being most strict, 6 being least strict.',
        examples=[2],
        ge=0,
        le=6,
        title='Safety Tolerance',
    )
    webhook_url: Optional[WebhookUrl] = Field(
        None, description='URL to receive webhook notifications', title='Webhook Url'
    )
    webhook_secret: Optional[str] = Field(
        None,
        description='Optional secret for webhook signature verification',
        title='Webhook Secret',
    )


class BFLHTTPValidationError(BaseModel):
    detail: Optional[List[BFLValidationError]] = Field(None, title='Detail')


class BFLFluxProExpandInputs(BaseModel):
    image: str = Field(
        ...,
        description='A Base64-encoded string representing the image you wish to expand.',
        title='Image',
    )
    top: Optional[Top] = Field(
        0, description='Number of pixels to expand at the top of the image', title='Top'
    )
    bottom: Optional[Bottom] = Field(
        0,
        description='Number of pixels to expand at the bottom of the image',
        title='Bottom',
    )
    left: Optional[Left] = Field(
        0,
        description='Number of pixels to expand on the left side of the image',
        title='Left',
    )
    right: Optional[Right] = Field(
        0,
        description='Number of pixels to expand on the right side of the image',
        title='Right',
    )
    prompt: Optional[str] = Field(
        '',
        description='The description of the changes you want to make. This text guides the expansion process, allowing you to specify features, styles, or modifications for the expanded areas.',
        examples=['ein fantastisches bild'],
        title='Prompt',
    )
    steps: Optional[Steps] = Field(
        default_factory=lambda: Steps.model_validate(50),
        description='Number of steps for the image generation process',
        examples=[50],
        title='Steps',
    )
    prompt_upsampling: Optional[bool] = Field(
        False,
        description='Whether to perform upsampling on the prompt. If active, automatically modifies the prompt for more creative generation',
        title='Prompt Upsampling',
    )
    seed: Optional[int] = Field(
        None, description='Optional seed for reproducibility', title='Seed'
    )
    guidance: Optional[Guidance] = Field(
        default_factory=lambda: Guidance.model_validate(60),
        description='Guidance strength for the image generation process',
        title='Guidance',
    )
    output_format: Optional[BFLOutputFormat] = Field(
        'jpeg',
        description="Output format for the generated image. Can be 'jpeg' or 'png'.",
    )
    safety_tolerance: Optional[int] = Field(
        2,
        description='Tolerance level for input and output moderation. Between 0 and 6, 0 being most strict, 6 being least strict.',
        examples=[2],
        ge=0,
        le=6,
        title='Safety Tolerance',
    )
    webhook_url: Optional[WebhookUrl] = Field(
        None, description='URL to receive webhook notifications', title='Webhook Url'
    )
    webhook_secret: Optional[str] = Field(
        None,
        description='Optional secret for webhook signature verification',
        title='Webhook Secret',
    )


class BFLCannyInputs(BaseModel):
    prompt: str = Field(
        ...,
        description='Text prompt for image generation',
        examples=['ein fantastisches bild'],
        title='Prompt',
    )
    control_image: Optional[str] = Field(
        None,
        description='Base64 encoded image to use as control input if no preprocessed image is provided',
        title='Control Image',
    )
    preprocessed_image: Optional[str] = Field(
        None,
        description='Optional pre-processed image that will bypass the control preprocessing step',
        title='Preprocessed Image',
    )
    canny_low_threshold: Optional[CannyLowThreshold] = Field(
        default_factory=lambda: CannyLowThreshold.model_validate(50),
        description='Low threshold for Canny edge detection',
        title='Canny Low Threshold',
    )
    canny_high_threshold: Optional[CannyHighThreshold] = Field(
        default_factory=lambda: CannyHighThreshold.model_validate(200),
        description='High threshold for Canny edge detection',
        title='Canny High Threshold',
    )
    prompt_upsampling: Optional[bool] = Field(
        False,
        description='Whether to perform upsampling on the prompt',
        title='Prompt Upsampling',
    )
    seed: Optional[int] = Field(
        None,
        description='Optional seed for reproducibility',
        examples=[42],
        title='Seed',
    )
    steps: Optional[Steps2] = Field(
        default_factory=lambda: Steps2.model_validate(50),
        description='Number of steps for the image generation process',
        title='Steps',
    )
    output_format: Optional[BFLOutputFormat] = Field(
        'jpeg',
        description="Output format for the generated image. Can be 'jpeg' or 'png'.",
    )
    guidance: Optional[Guidance2] = Field(
        default_factory=lambda: Guidance2.model_validate(30),
        description='Guidance strength for the image generation process',
        title='Guidance',
    )
    safety_tolerance: Optional[int] = Field(
        2,
        description='Tolerance level for input and output moderation. Between 0 and 6, 0 being most strict, 6 being least strict.',
        ge=0,
        le=6,
        title='Safety Tolerance',
    )
    webhook_url: Optional[WebhookUrl] = Field(
        None, description='URL to receive webhook notifications', title='Webhook Url'
    )
    webhook_secret: Optional[str] = Field(
        None,
        description='Optional secret for webhook signature verification',
        title='Webhook Secret',
    )


class BFLDepthInputs(BaseModel):
    prompt: str = Field(
        ...,
        description='Text prompt for image generation',
        examples=['ein fantastisches bild'],
        title='Prompt',
    )
    control_image: Optional[str] = Field(
        None,
        description='Base64 encoded image to use as control input',
        title='Control Image',
    )
    preprocessed_image: Optional[str] = Field(
        None,
        description='Optional pre-processed image that will bypass the control preprocessing step',
        title='Preprocessed Image',
    )
    prompt_upsampling: Optional[bool] = Field(
        False,
        description='Whether to perform upsampling on the prompt',
        title='Prompt Upsampling',
    )
    seed: Optional[int] = Field(
        None,
        description='Optional seed for reproducibility',
        examples=[42],
        title='Seed',
    )
    steps: Optional[Steps2] = Field(
        default_factory=lambda: Steps2.model_validate(50),
        description='Number of steps for the image generation process',
        title='Steps',
    )
    output_format: Optional[BFLOutputFormat] = Field(
        'jpeg',
        description="Output format for the generated image. Can be 'jpeg' or 'png'.",
    )
    guidance: Optional[Guidance2] = Field(
        default_factory=lambda: Guidance2.model_validate(15),
        description='Guidance strength for the image generation process',
        title='Guidance',
    )
    safety_tolerance: Optional[int] = Field(
        2,
        description='Tolerance level for input and output moderation. Between 0 and 6, 0 being most strict, 6 being least strict.',
        ge=0,
        le=6,
        title='Safety Tolerance',
    )
    webhook_url: Optional[WebhookUrl] = Field(
        None, description='URL to receive webhook notifications', title='Webhook Url'
    )
    webhook_secret: Optional[str] = Field(
        None,
        description='Optional secret for webhook signature verification',
        title='Webhook Secret',
    )


class Controls(BaseModel):
    artistic_level: Optional[int] = Field(
        None,
        description='Defines artistic tone of your image. At a simple level, the person looks straight at the camera in a static and clean style. Dynamic and eccentric levels introduce movement and creativity.',
        ge=0,
        le=5,
    )
    colors: Optional[List[RGBColor]] = Field(
        None, description='An array of preferable colors'
    )
    background_color: Optional[RGBColor] = Field(
        None, description='Use given color as a desired background color'
    )
    no_text: Optional[bool] = Field(None, description='Do not embed text layouts')


class RecraftImageGenerationRequest(BaseModel):
    prompt: str = Field(
        ..., description='The text prompt describing the image to generate'
    )
    model: str = Field(
        ..., description='The model to use for generation (e.g., "recraftv3")'
    )
    style: Optional[str] = Field(
        None,
        description='The style to apply to the generated image (e.g., "digital_illustration")',
    )
    style_id: Optional[str] = Field(
        None,
        description='The style ID to apply to the generated image (e.g., "123e4567-e89b-12d3-a456-426614174000"). If style_id is provided, style should not be provided.',
    )
    size: str = Field(
        ..., description='The size of the generated image (e.g., "1024x1024")'
    )
    controls: Optional[Controls] = Field(
        None, description='The controls for the generated image'
    )
    n: int = Field(..., description='The number of images to generate', ge=1, le=4)


class LumaKeyframes(BaseModel):
    frame0: Optional[LumaKeyframe] = None
    frame1: Optional[LumaKeyframe] = None


class LumaGenerationRequest(BaseModel):
    generation_type: Optional[GenerationType] = 'video'
    prompt: str = Field(..., description='The prompt of the generation')
    aspect_ratio: LumaAspectRatio
    loop: Optional[bool] = Field(None, description='Whether to loop the video')
    keyframes: Optional[LumaKeyframes] = None
    callback_url: Optional[AnyUrl] = Field(
        None,
        description='The callback URL of the generation, a POST request with Generation object will be sent to the callback URL when the generation is dreaming, completed, or failed',
    )
    model: LumaVideoModel
    resolution: LumaVideoModelOutputResolution
    duration: LumaVideoModelOutputDuration


class LumaGeneration(BaseModel):
    id: Optional[UUID] = Field(None, description='The ID of the generation')
    generation_type: Optional[LumaGenerationType] = None
    state: Optional[LumaState] = None
    failure_reason: Optional[str] = Field(
        None, description='The reason for the state of the generation'
    )
    created_at: Optional[datetime] = Field(
        None, description='The date and time when the generation was created'
    )
    assets: Optional[LumaAssets] = None
    model: Optional[str] = Field(None, description='The model used for the generation')
    request: Optional[
        Union[
            LumaGenerationRequest,
            LumaImageGenerationRequest,
            LumaUpscaleVideoGenerationRequest,
            LumaAudioGenerationRequest,
        ]
    ] = Field(None, description='The request of the generation')


class RunwayImageToVideoRequest(BaseModel):
    promptImage: RunwayPromptImageObject
    seed: int = Field(
        ..., description='Random seed for generation', ge=0, le=4294967295
    )
    model: RunwayModelEnum = Field(..., description='Model to use for generation')
    promptText: Optional[str] = Field(
        None, description='Text prompt for the generation', max_length=1000
    )
    duration: RunwayDurationEnum = Field(
        ..., description='The number of seconds of duration for the output video.'
    )
    ratio: RunwayAspectRatioEnum = Field(
        ...,
        description='The resolution (aspect ratio) of the output video. Allowable values depend on the selected model. 1280:768 and 768:1280 are only supported for gen3a_turbo.',
    )


class RunwayTaskStatusResponse(BaseModel):
    id: Optional[str] = Field(None, description='Task ID')
    status: Optional[RunwayTaskStatusEnum] = Field(None, description='Task status')
    createdAt: Optional[datetime] = Field(None, description='Task creation timestamp')
    output: Optional[List[str]] = Field(None, description='Array of output video URLs')


class PikaHTTPValidationError(BaseModel):
    detail: Optional[List[PikaValidationError]] = Field(None, title='Detail')


class PikaBodyGenerate22T2vGenerate22T2vPost(BaseModel):
    promptText: str = Field(..., title='Prompttext')
    negativePrompt: Optional[str] = Field(None, title='Negativeprompt')
    seed: Optional[int] = Field(None, title='Seed')
    resolution: Optional[PikaResolutionEnum] = Field('1080p', title='Resolution')
    duration: Optional[PikaDurationEnum] = Field(5, title='Duration')
    aspectRatio: Optional[float] = Field(
        1.7777777777777777,
        description='Aspect ratio (width / height)',
        ge=0.4,
        le=2.5,
        title='Aspectratio',
    )


class PikaBodyGenerate22I2vGenerate22I2vPost(BaseModel):
    image: Optional[StrictBytes] = Field(None, title='Image')
    promptText: Optional[str] = Field(None, title='Prompttext')
    negativePrompt: Optional[str] = Field(None, title='Negativeprompt')
    seed: Optional[int] = Field(None, title='Seed')
    resolution: Optional[PikaResolutionEnum] = Field('1080p', title='Resolution')
    duration: Optional[PikaDurationEnum] = Field(5, title='Duration')


class PikaBodyGenerate22KeyframeGenerate22PikaframesPost(BaseModel):
    keyFrames: Optional[List[StrictBytes]] = Field(
        None, description='Array of keyframe images', title='Keyframes'
    )
    promptText: str = Field(..., title='Prompttext')
    negativePrompt: Optional[str] = Field(None, title='Negativeprompt')
    seed: Optional[int] = Field(None, title='Seed')
    resolution: Optional[PikaResolutionEnum] = Field('1080p', title='Resolution')
    duration: Optional[int] = Field(None, ge=5, le=10, title='Duration')


class PikaVideoResponse(BaseModel):
    id: str = Field(..., title='Id')
    status: PikaStatusEnum = Field(
        ..., description='The status of the video', title='Status'
    )
    url: Optional[str] = Field(None, title='Url')
    progress: Optional[int] = Field(None, title='Progress')


class Node(BaseModel):
    id: Optional[str] = Field(None, description='The unique identifier of the node.')
    name: Optional[str] = Field(None, description='The display name of the node.')
    category: Optional[str] = Field(None, description='The category of the node.')
    description: Optional[str] = None
    author: Optional[str] = None
    license: Optional[str] = Field(
        None, description="The path to the LICENSE file in the node's repository."
    )
    icon: Optional[str] = Field(None, description="URL to the node's icon.")
    repository: Optional[str] = Field(None, description="URL to the node's repository.")
    tags: Optional[List[str]] = None
    latest_version: Optional[NodeVersion] = Field(
        None, description='The latest version of the node.'
    )
    rating: Optional[float] = Field(None, description='The average rating of the node.')
    downloads: Optional[int] = Field(
        None, description='The number of downloads of the node.'
    )
    publisher: Optional[Publisher] = Field(
        None, description='The publisher of the node.'
    )
    status: Optional[NodeStatus] = Field(None, description='The status of the node.')
    status_detail: Optional[str] = Field(
        None, description='The status detail of the node.'
    )
    translations: Optional[Dict[str, Dict[str, Any]]] = None


class KlingVideoEffectsRequest(BaseModel):
    effect_scene: Union[KlingDualCharacterEffectsScene, KlingSingleImageEffectsScene]
    input: KlingVideoEffectsInput
    callback_url: Optional[AnyUrl] = Field(
        None,
        description='The callback notification address for the result of this task.',
    )
    external_task_id: Optional[str] = Field(
        None,
        description='Customized Task ID. Must be unique within a single user account.',
    )


class StripeCharge(BaseModel):
    id: Optional[str] = None
    object: Optional[Object2] = None
    amount: Optional[int] = None
    amount_captured: Optional[int] = None
    amount_refunded: Optional[int] = None
    application: Optional[str] = None
    application_fee: Optional[str] = None
    application_fee_amount: Optional[int] = None
    balance_transaction: Optional[str] = None
    billing_details: Optional[StripeBillingDetails] = None
    calculated_statement_descriptor: Optional[str] = None
    captured: Optional[bool] = None
    created: Optional[int] = None
    currency: Optional[str] = None
    customer: Optional[str] = None
    description: Optional[str] = None
    destination: Optional[Any] = None
    dispute: Optional[Any] = None
    disputed: Optional[bool] = None
    failure_balance_transaction: Optional[Any] = None
    failure_code: Optional[Any] = None
    failure_message: Optional[Any] = None
    fraud_details: Optional[Dict[str, Any]] = None
    invoice: Optional[Any] = None
    livemode: Optional[bool] = None
    metadata: Optional[Dict[str, Any]] = None
    on_behalf_of: Optional[Any] = None
    order: Optional[Any] = None
    outcome: Optional[StripeOutcome] = None
    paid: Optional[bool] = None
    payment_intent: Optional[str] = None
    payment_method: Optional[str] = None
    payment_method_details: Optional[StripePaymentMethodDetails] = None
    radar_options: Optional[Dict[str, Any]] = None
    receipt_email: Optional[str] = None
    receipt_number: Optional[str] = None
    receipt_url: Optional[str] = None
    refunded: Optional[bool] = None
    refunds: Optional[StripeRefundList] = None
    review: Optional[Any] = None
    shipping: Optional[StripeShipping] = None
    source: Optional[Any] = None
    source_transfer: Optional[Any] = None
    statement_descriptor: Optional[Any] = None
    statement_descriptor_suffix: Optional[Any] = None
    status: Optional[str] = None
    transfer_data: Optional[Any] = None
    transfer_group: Optional[Any] = None


class StripeChargeList(BaseModel):
    object: Optional[str] = None
    data: Optional[List[StripeCharge]] = None
    has_more: Optional[bool] = None
    total_count: Optional[int] = None
    url: Optional[str] = None


class StripePaymentIntent(BaseModel):
    id: Optional[str] = None
    object: Optional[Object1] = None
    amount: Optional[int] = None
    amount_capturable: Optional[int] = None
    amount_details: Optional[StripeAmountDetails] = None
    amount_received: Optional[int] = None
    application: Optional[str] = None
    application_fee_amount: Optional[int] = None
    automatic_payment_methods: Optional[Any] = None
    canceled_at: Optional[int] = None
    cancellation_reason: Optional[str] = None
    capture_method: Optional[str] = None
    charges: Optional[StripeChargeList] = None
    client_secret: Optional[str] = None
    confirmation_method: Optional[str] = None
    created: Optional[int] = None
    currency: Optional[str] = None
    customer: Optional[str] = None
    description: Optional[str] = None
    invoice: Optional[str] = None
    last_payment_error: Optional[Any] = None
    latest_charge: Optional[str] = None
    livemode: Optional[bool] = None
    metadata: Optional[Dict[str, Any]] = None
    next_action: Optional[Any] = None
    on_behalf_of: Optional[Any] = None
    payment_method: Optional[str] = None
    payment_method_configuration_details: Optional[Any] = None
    payment_method_options: Optional[StripePaymentMethodOptions] = None
    payment_method_types: Optional[List[str]] = None
    processing: Optional[Any] = None
    receipt_email: Optional[str] = None
    review: Optional[Any] = None
    setup_future_usage: Optional[Any] = None
    shipping: Optional[StripeShipping] = None
    source: Optional[Any] = None
    statement_descriptor: Optional[Any] = None
    statement_descriptor_suffix: Optional[Any] = None
    status: Optional[str] = None
    transfer_data: Optional[Any] = None
    transfer_group: Optional[Any] = None


class Data8(BaseModel):
    object: Optional[StripePaymentIntent] = None


class StripeEvent(BaseModel):
    id: str
    object: Object
    api_version: Optional[str] = None
    created: Optional[int] = None
    data: Data8
    livemode: Optional[bool] = None
    pending_webhooks: Optional[int] = None
    request: Optional[StripeRequestInfo] = None
    type: Type
